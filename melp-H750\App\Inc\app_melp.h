/* SPDX-License-Identifier: MIT */
/**
 * @file app_melp.h
 * @brief MELP application interface
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

#ifndef APP_MELP_H
#define APP_MELP_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "project_config.h"
#include "melp_encoder.h"
#include "melp_decoder.h"

/* ========================================================================== */
/*                           TYPE DEFINITIONS                                */
/* ========================================================================== */

/**
 * @brief MELP application states
 */
typedef enum {
    APP_MELP_STATE_IDLE = 0,
    APP_MELP_STATE_RECORDING,
    APP_MELP_STATE_ENCODING,
    APP_MELP_STATE_TRANSMITTING,
    APP_MELP_STATE_RECEIVING,
    APP_MELP_STATE_DECODING,
    APP_MELP_STATE_PLAYING,
    APP_MELP_STATE_ERROR
} app_melp_state_t;

/**
 * @brief MELP application context
 */
typedef struct {
    melp_encoder_state_t encoder_state;
    melp_decoder_state_t decoder_state;
    app_melp_state_t current_state;
    
    // Audio buffers
    int16_t input_buffer[AUDIO_INPUT_BUFFER_SIZE];
    int16_t output_buffer[AUDIO_OUTPUT_BUFFER_SIZE];
    uint32_t input_samples_count;
    uint32_t output_samples_count;
    
    // MELP frame buffers
    uint8_t encoded_frame_buffer[MELP_ENCODED_BUFFER_SIZE];
    uint32_t encoded_frames_count;
    
    // Statistics
    uint32_t frames_encoded;
    uint32_t frames_decoded;
    uint32_t frames_transmitted;
    uint32_t frames_received;
    uint32_t frame_errors;
    
    bool initialized;
} app_melp_context_t;

/**
 * @brief MELP application callbacks
 */
typedef struct {
    void (*on_frame_encoded)(const uint8_t *frame_data, uint8_t frame_size);
    void (*on_frame_decoded)(const int16_t *audio_data, uint32_t sample_count);
    void (*on_state_changed)(app_melp_state_t old_state, app_melp_state_t new_state);
    void (*on_error)(uint32_t error_code);
} app_melp_callbacks_t;

/* ========================================================================== */
/*                           FUNCTION DECLARATIONS                           */
/* ========================================================================== */

/**
 * @brief Initialize MELP application
 * @param context Pointer to application context
 * @param callbacks Pointer to callback functions
 * @return true if successful, false otherwise
 */
bool app_melp_init(app_melp_context_t *context, const app_melp_callbacks_t *callbacks);

/**
 * @brief Deinitialize MELP application
 * @param context Pointer to application context
 * @return true if successful, false otherwise
 */
bool app_melp_deinit(app_melp_context_t *context);

/**
 * @brief Process audio input samples
 * @param context Pointer to application context
 * @param samples Input audio samples
 * @param sample_count Number of samples
 * @return true if successful, false otherwise
 */
bool app_melp_process_input(app_melp_context_t *context, 
                            const int16_t *samples, 
                            uint32_t sample_count);

/**
 * @brief Process received MELP frame
 * @param context Pointer to application context
 * @param frame_data Received frame data
 * @param frame_size Frame size in bytes
 * @return true if successful, false otherwise
 */
bool app_melp_process_received_frame(app_melp_context_t *context,
                                     const uint8_t *frame_data,
                                     uint8_t frame_size);

/**
 * @brief Get current application state
 * @param context Pointer to application context
 * @return Current application state
 */
app_melp_state_t app_melp_get_state(const app_melp_context_t *context);

/**
 * @brief Get application statistics
 * @param context Pointer to application context
 * @param frames_encoded Output: number of frames encoded
 * @param frames_decoded Output: number of frames decoded
 * @param frame_errors Output: number of frame errors
 * @return true if successful, false otherwise
 */
bool app_melp_get_statistics(const app_melp_context_t *context,
                             uint32_t *frames_encoded,
                             uint32_t *frames_decoded,
                             uint32_t *frame_errors);

/**
 * @brief Reset application statistics
 * @param context Pointer to application context
 * @return true if successful, false otherwise
 */
bool app_melp_reset_statistics(app_melp_context_t *context);

/**
 * @brief Start recording mode
 * @param context Pointer to application context
 * @return true if successful, false otherwise
 */
bool app_melp_start_recording(app_melp_context_t *context);

/**
 * @brief Stop recording mode
 * @param context Pointer to application context
 * @return true if successful, false otherwise
 */
bool app_melp_stop_recording(app_melp_context_t *context);

/**
 * @brief Start playback mode
 * @param context Pointer to application context
 * @return true if successful, false otherwise
 */
bool app_melp_start_playback(app_melp_context_t *context);

/**
 * @brief Stop playback mode
 * @param context Pointer to application context
 * @return true if successful, false otherwise
 */
bool app_melp_stop_playback(app_melp_context_t *context);

#ifdef __cplusplus
}
#endif

#endif /* APP_MELP_H */
