/* SPDX-License-Identifier: MIT */
/**
 * @file melp_encoder_optimized.c
 * @brief Optimized MELP encoder using CMSIS-DSP
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

#include "../inc/melp_encoder.h"
#include "../inc/melp_dsp.h"
#include <string.h>
#include <math.h>

/* ========================================================================== */
/*                           PRIVATE VARIABLES                               */
/* ========================================================================== */

static melp_dsp_context_t dsp_context;
static float32_t hamming_window[MELP_FRAME_SIZE];
static bool optimized_encoder_initialized = false;

/* ========================================================================== */
/*                           PRIVATE FUNCTION DECLARATIONS                   */
/* ========================================================================== */

static void melp_init_hamming_window_optimized(void);
static bool melp_lpc_analysis_optimized(const float32_t *input, float32_t *lpc_coeffs, float32_t *gain);
static bool melp_pitch_estimation_optimized(const float32_t *input, float32_t *pitch);
static bool melp_voicing_analysis_optimized(const float32_t *input, float32_t pitch, float32_t *voicing);

/* ========================================================================== */
/*                           PUBLIC FUNCTION IMPLEMENTATIONS                 */
/* ========================================================================== */

bool melp_encoder_init_optimized(melp_encoder_state_t *state)
{
    if (state == NULL) {
        return false;
    }

    // Initialize base encoder
    if (!melp_encoder_init(state)) {
        return false;
    }

    // Initialize DSP context
    if (!melp_dsp_init(&dsp_context)) {
        return false;
    }

    // Initialize Hamming window
    if (!optimized_encoder_initialized) {
        melp_init_hamming_window_optimized();
        optimized_encoder_initialized = true;
    }

    return true;
}

bool melp_encoder_deinit_optimized(melp_encoder_state_t *state)
{
    if (state == NULL) {
        return false;
    }

    // Deinitialize DSP context
    melp_dsp_deinit(&dsp_context);

    // Deinitialize base encoder
    return melp_encoder_deinit(state);
}

bool melp_encoder_encode_frame_optimized(melp_encoder_state_t *state,
                                         const int16_t *input_samples,
                                         melp_encoded_frame_t *encoded_frame)
{
    if (state == NULL || input_samples == NULL || encoded_frame == NULL) {
        return false;
    }

    if (!state->initialized) {
        return false;
    }

    float32_t speech_buffer[MELP_FRAME_SIZE];
    float32_t preemph_buffer[MELP_FRAME_SIZE];
    melp_params_t params;

    // Convert input to float
    for (uint32_t i = 0; i < MELP_FRAME_SIZE; i++) {
        speech_buffer[i] = (float32_t)input_samples[i];
    }

    // Pre-emphasis filtering (optimized)
    if (!melp_dsp_preemphasis(&dsp_context, speech_buffer, preemph_buffer, MELP_FRAME_SIZE)) {
        return false;
    }

    // LPC analysis (optimized)
    if (!melp_lpc_analysis_optimized(preemph_buffer, params.lpc_coeffs, &params.gain)) {
        return false;
    }

    // Convert LPC to LSF (using existing function)
    if (!melp_lpc_to_lsf(params.lpc_coeffs, params.lsf_coeffs)) {
        return false;
    }

    // Pitch estimation (optimized)
    if (!melp_pitch_estimation_optimized(preemph_buffer, &params.pitch)) {
        return false;
    }

    // Voicing analysis (optimized)
    if (!melp_voicing_analysis_optimized(preemph_buffer, params.pitch, params.voicing)) {
        return false;
    }

    // Set aperiodic flag based on voicing
    float32_t avg_voicing = 0.0f;
    for (int i = 0; i < MELP_NUM_BANDS; i++) {
        avg_voicing += params.voicing[i];
    }
    avg_voicing /= MELP_NUM_BANDS;
    params.aperiodic_flag = (avg_voicing < MELP_VOICING_THRESH);

    // Quantize parameters (using existing function)
    if (!melp_quantize_parameters(&params, encoded_frame)) {
        return false;
    }

    // Update state
    state->pitch_mem = params.pitch;
    state->gain_mem = params.gain;
    state->frame_count++;

    return true;
}

/* ========================================================================== */
/*                           PRIVATE FUNCTION IMPLEMENTATIONS                */
/* ========================================================================== */

static void melp_init_hamming_window_optimized(void)
{
    for (uint32_t i = 0; i < MELP_FRAME_SIZE; i++) {
        hamming_window[i] = 0.54f - 0.46f * cosf(2.0f * 3.14159265f * i / (MELP_FRAME_SIZE - 1));
    }
}

static bool melp_lpc_analysis_optimized(const float32_t *input, float32_t *lpc_coeffs, float32_t *gain)
{
    if (input == NULL || lpc_coeffs == NULL || gain == NULL) {
        return false;
    }

    float32_t windowed_speech[MELP_FRAME_SIZE];
    float32_t autocorr[MELP_LPC_ORDER_ALG + 1];

    // Apply Hamming window (optimized)
    if (!melp_dsp_windowing(input, hamming_window, windowed_speech, MELP_FRAME_SIZE)) {
        return false;
    }

    // Compute autocorrelation (optimized)
    if (!melp_dsp_autocorrelation(&dsp_context, windowed_speech, autocorr, 
                                  MELP_FRAME_SIZE, MELP_LPC_ORDER_ALG)) {
        return false;
    }

    // Levinson-Durbin algorithm (optimized)
    float32_t error;
    if (!melp_dsp_levinson_durbin(autocorr, lpc_coeffs, MELP_LPC_ORDER_ALG, &error)) {
        return false;
    }

    // Calculate gain from prediction error
    *gain = sqrtf(error);

    return true;
}

static bool melp_pitch_estimation_optimized(const float32_t *input, float32_t *pitch)
{
    if (input == NULL || pitch == NULL) {
        return false;
    }

    float32_t correlation;
    
    // Use optimized pitch estimation
    if (!melp_dsp_pitch_estimation(&dsp_context, input, MELP_PITCH_MIN, MELP_PITCH_MAX, 
                                   pitch, &correlation)) {
        return false;
    }

    return true;
}

static bool melp_voicing_analysis_optimized(const float32_t *input, float32_t pitch, float32_t *voicing)
{
    if (input == NULL || voicing == NULL) {
        return false;
    }

    // Calculate signal energy using optimized dot product
    float32_t energy = melp_dsp_dot_product(input, input, MELP_FRAME_SIZE);
    energy = sqrtf(energy / MELP_FRAME_SIZE);

    // Perform spectral analysis for voicing decision
    float32_t magnitude[MELP_FFT_SIZE / 2];
    if (!melp_dsp_spectral_analysis(&dsp_context, input, magnitude, NULL, MELP_FRAME_SIZE)) {
        return false;
    }

    // Analyze spectral characteristics for voicing decision
    float32_t spectral_flatness = 0.0f;
    float32_t spectral_centroid = 0.0f;
    float32_t total_energy = 0.0f;

    // Calculate spectral features
    for (uint32_t i = 1; i < MELP_FFT_SIZE / 4; i++) {  // Focus on lower frequencies
        total_energy += magnitude[i];
        spectral_centroid += i * magnitude[i];
    }

    if (total_energy > 0.0f) {
        spectral_centroid /= total_energy;
        
        // Calculate spectral flatness (simplified)
        float32_t geometric_mean = 1.0f;
        float32_t arithmetic_mean = 0.0f;
        uint32_t count = 0;
        
        for (uint32_t i = 1; i < MELP_FFT_SIZE / 4; i++) {
            if (magnitude[i] > 0.0f) {
                geometric_mean *= powf(magnitude[i], 1.0f / (MELP_FFT_SIZE / 4 - 1));
                arithmetic_mean += magnitude[i];
                count++;
            }
        }
        
        if (count > 0) {
            arithmetic_mean /= count;
            spectral_flatness = (arithmetic_mean > 0.0f) ? geometric_mean / arithmetic_mean : 0.0f;
        }
    }

    // Voicing decision based on multiple criteria
    float32_t voicing_strength = 0.0f;
    
    // Energy-based voicing
    if (energy > 100.0f) {
        voicing_strength += 0.3f;
    }
    
    // Pitch-based voicing
    if (pitch > MELP_PITCH_MIN && pitch < MELP_PITCH_MAX) {
        voicing_strength += 0.4f;
    }
    
    // Spectral-based voicing
    if (spectral_flatness < 0.5f) {  // More tonal
        voicing_strength += 0.3f;
    }

    // Distribute voicing across frequency bands
    for (int i = 0; i < MELP_NUM_BANDS; i++) {
        // Lower bands typically more voiced
        float32_t band_factor = 1.0f - (float32_t)i / MELP_NUM_BANDS;
        voicing[i] = voicing_strength * band_factor;
        
        // Clamp to [0, 1]
        if (voicing[i] > 1.0f) voicing[i] = 1.0f;
        if (voicing[i] < 0.0f) voicing[i] = 0.0f;
    }

    return true;
}

/* ========================================================================== */
/*                           WRAPPER FUNCTIONS                               */
/* ========================================================================== */

// Provide optimized versions as drop-in replacements
bool melp_encoder_init_fast(melp_encoder_state_t *state)
{
    return melp_encoder_init_optimized(state);
}

bool melp_encoder_encode_frame_fast(melp_encoder_state_t *state,
                                    const int16_t *input_samples,
                                    melp_encoded_frame_t *encoded_frame)
{
    return melp_encoder_encode_frame_optimized(state, input_samples, encoded_frame);
}

bool melp_encoder_deinit_fast(melp_encoder_state_t *state)
{
    return melp_encoder_deinit_optimized(state);
}
