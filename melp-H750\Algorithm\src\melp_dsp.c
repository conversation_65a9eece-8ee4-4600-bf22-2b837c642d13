/* SPDX-License-Identifier: MIT */
/**
 * @file melp_dsp.c
 * @brief MELP DSP optimized functions implementation
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

#include "../inc/melp_dsp.h"
#include <string.h>
#include <math.h>

/* ========================================================================== */
/*                           PRIVATE CONSTANTS                               */
/* ========================================================================== */

#define MELP_PI                     3.14159265358979323846f
#define MELP_EPSILON                1e-10f

/* ========================================================================== */
/*                           PUBLIC FUNCTION IMPLEMENTATIONS                 */
/* ========================================================================== */

bool melp_dsp_init(melp_dsp_context_t *context)
{
    if (context == NULL) {
        return false;
    }

    // Clear context
    memset(context, 0, sizeof(melp_dsp_context_t));

#if MELP_USE_CMSIS_DSP
    // Initialize CMSIS-DSP instances
    arm_status status;
    
    // Initialize RFFT instance
    status = arm_rfft_fast_init_f32(&context->rfft_instance, MELP_FFT_SIZE);
    if (status != ARM_MATH_SUCCESS) {
        return false;
    }
    
    // Initialize pre-emphasis filter (1st order FIR)
    context->preemph_coeffs[0] = 1.0f;
    context->preemph_coeffs[1] = -0.8f;
    arm_fir_init_f32(&context->preemph_filter, 2, context->preemph_coeffs, 
                     context->preemph_state, 1);
    
    // Initialize post-filter (biquad cascade)
    // Simplified coefficients - actual implementation would use proper design
    for (int i = 0; i < 10; i++) {
        context->postfilt_coeffs[i] = (i == 0) ? 1.0f : 0.1f;
    }
    arm_biquad_cascade_df1_init_f32(&context->postfilter, 2, 
                                    context->postfilt_coeffs, 
                                    context->postfilt_state);
#endif

    context->initialized = true;
    return true;
}

bool melp_dsp_deinit(melp_dsp_context_t *context)
{
    if (context == NULL) {
        return false;
    }

    memset(context, 0, sizeof(melp_dsp_context_t));
    return true;
}

bool melp_dsp_preemphasis(melp_dsp_context_t *context,
                          const float32_t *input,
                          float32_t *output,
                          uint32_t length)
{
    if (context == NULL || input == NULL || output == NULL || !context->initialized) {
        return false;
    }

#if MELP_USE_CMSIS_DSP
    // Use CMSIS-DSP FIR filter
    arm_fir_f32(&context->preemph_filter, input, output, length);
#else
    // Fallback implementation
    float32_t prev = context->preemph_state[0];
    for (uint32_t i = 0; i < length; i++) {
        output[i] = input[i] - 0.8f * prev;
        prev = input[i];
    }
    context->preemph_state[0] = prev;
#endif

    return true;
}

bool melp_dsp_windowing(const float32_t *input,
                        const float32_t *window,
                        float32_t *output,
                        uint32_t length)
{
    if (input == NULL || window == NULL || output == NULL) {
        return false;
    }

#if MELP_USE_CMSIS_DSP
    // Use CMSIS-DSP vector multiplication
    arm_mult_f32(input, window, output, length);
#else
    // Fallback implementation
    for (uint32_t i = 0; i < length; i++) {
        output[i] = input[i] * window[i];
    }
#endif

    return true;
}

bool melp_dsp_autocorrelation(melp_dsp_context_t *context,
                              const float32_t *input,
                              float32_t *autocorr,
                              uint32_t length,
                              uint32_t order)
{
    if (context == NULL || input == NULL || autocorr == NULL || !context->initialized) {
        return false;
    }

#if MELP_USE_CMSIS_DSP
    // Use CMSIS-DSP correlation function
    for (uint32_t k = 0; k <= order; k++) {
        if (k < length) {
            arm_dot_prod_f32(input, &input[k], length - k, &autocorr[k]);
        } else {
            autocorr[k] = 0.0f;
        }
    }
#else
    // Fallback implementation
    for (uint32_t k = 0; k <= order; k++) {
        autocorr[k] = 0.0f;
        for (uint32_t i = 0; i < length - k; i++) {
            autocorr[k] += input[i] * input[i + k];
        }
    }
#endif

    return true;
}

bool melp_dsp_levinson_durbin(const float32_t *autocorr,
                              float32_t *lpc_coeffs,
                              uint32_t order,
                              float32_t *error)
{
    if (autocorr == NULL || lpc_coeffs == NULL) {
        return false;
    }

    float32_t alpha[MELP_LPC_ORDER + 1][MELP_LPC_ORDER + 1];
    float32_t err = autocorr[0];
    
    if (err <= MELP_EPSILON) {
        return false;
    }
    
    // Levinson-Durbin recursion (optimized version)
    for (uint32_t i = 1; i <= order; i++) {
        float32_t sum = 0.0f;
        
#if MELP_USE_CMSIS_DSP
        // Use CMSIS-DSP dot product for sum calculation
        arm_dot_prod_f32(&alpha[i-1][1], &autocorr[1], i-1, &sum);
        sum = autocorr[i] + sum;
#else
        for (uint32_t j = 1; j < i; j++) {
            sum += alpha[i-1][j] * autocorr[i-j];
        }
        sum = autocorr[i] + sum;
#endif
        
        float32_t k = -sum / err;
        alpha[i][i] = k;
        
        for (uint32_t j = 1; j < i; j++) {
            alpha[i][j] = alpha[i-1][j] + k * alpha[i-1][i-j];
        }
        
        err *= (1.0f - k * k);
        if (err <= MELP_EPSILON) {
            return false;
        }
    }
    
    // Copy final coefficients
    lpc_coeffs[0] = 1.0f;
    for (uint32_t i = 1; i <= order; i++) {
        lpc_coeffs[i] = alpha[order][i];
    }
    
    if (error != NULL) {
        *error = err;
    }
    
    return true;
}

bool melp_dsp_lpc_synthesis(const float32_t *lpc_coeffs,
                            const float32_t *excitation,
                            float32_t *output,
                            uint32_t length,
                            uint32_t order,
                            float32_t *memory)
{
    if (lpc_coeffs == NULL || excitation == NULL || output == NULL) {
        return false;
    }

    // IIR synthesis filter implementation
    for (uint32_t i = 0; i < length; i++) {
        float32_t sum = excitation[i];
        
        // Feedback from previous outputs
        for (uint32_t j = 1; j <= order; j++) {
            if (i >= j) {
                sum -= lpc_coeffs[j] * output[i - j];
            } else if (memory != NULL) {
                sum -= lpc_coeffs[j] * memory[order - j + i];
            }
        }
        
        output[i] = sum;
    }

    // Update memory if provided
    if (memory != NULL) {
        for (uint32_t i = 0; i < order; i++) {
            if (length > order) {
                memory[i] = output[length - order + i];
            } else {
                memory[i] = output[i];
            }
        }
    }

    return true;
}

bool melp_dsp_pitch_estimation(melp_dsp_context_t *context,
                               const float32_t *input,
                               uint32_t pitch_min,
                               uint32_t pitch_max,
                               float32_t *pitch,
                               float32_t *correlation)
{
    if (context == NULL || input == NULL || pitch == NULL || !context->initialized) {
        return false;
    }

    float32_t max_corr = 0.0f;
    uint32_t best_lag = pitch_min;

    for (uint32_t lag = pitch_min; lag <= pitch_max; lag++) {
        float32_t corr;
        
#if MELP_USE_CMSIS_DSP
        // Use CMSIS-DSP dot product
        arm_dot_prod_f32(input, &input[lag], AUDIO_FRAME_LENGTH_SAMPLES - lag, &corr);
#else
        corr = 0.0f;
        for (uint32_t i = 0; i < AUDIO_FRAME_LENGTH_SAMPLES - lag; i++) {
            corr += input[i] * input[i + lag];
        }
#endif
        
        if (corr > max_corr) {
            max_corr = corr;
            best_lag = lag;
        }
    }

    *pitch = (float32_t)best_lag;
    if (correlation != NULL) {
        *correlation = max_corr;
    }

    return true;
}

bool melp_dsp_spectral_analysis(melp_dsp_context_t *context,
                                const float32_t *input,
                                float32_t *magnitude,
                                float32_t *phase,
                                uint32_t length)
{
    if (context == NULL || input == NULL || magnitude == NULL || !context->initialized) {
        return false;
    }

    if (length > MELP_FFT_SIZE) {
        return false;
    }

#if MELP_USE_CMSIS_DSP
    // Copy input to FFT buffer and zero-pad if necessary
    memcpy(context->fft_buffer, input, length * sizeof(float32_t));
    if (length < MELP_FFT_SIZE) {
        memset(&context->fft_buffer[length], 0, (MELP_FFT_SIZE - length) * sizeof(float32_t));
    }

    // Perform RFFT
    arm_rfft_fast_f32(&context->rfft_instance, context->fft_buffer, context->fft_buffer, 0);

    // Calculate magnitude and phase
    arm_cmplx_mag_f32(context->fft_buffer, magnitude, MELP_FFT_SIZE / 2);
    
    if (phase != NULL) {
        for (uint32_t i = 0; i < MELP_FFT_SIZE / 2; i++) {
            phase[i] = atan2f(context->fft_buffer[2*i + 1], context->fft_buffer[2*i]);
        }
    }
#else
    // Fallback: simplified DFT (very slow, for testing only)
    for (uint32_t k = 0; k < length / 2; k++) {
        float32_t real = 0.0f, imag = 0.0f;
        for (uint32_t n = 0; n < length; n++) {
            float32_t angle = -2.0f * MELP_PI * k * n / length;
            real += input[n] * cosf(angle);
            imag += input[n] * sinf(angle);
        }
        magnitude[k] = sqrtf(real * real + imag * imag);
        if (phase != NULL) {
            phase[k] = atan2f(imag, real);
        }
    }
#endif

    return true;
}

bool melp_dsp_post_filter(melp_dsp_context_t *context,
                          const float32_t *input,
                          const float32_t *lpc_coeffs,
                          float32_t *output,
                          uint32_t length)
{
    if (context == NULL || input == NULL || output == NULL || !context->initialized) {
        return false;
    }

#if MELP_USE_CMSIS_DSP
    // Use CMSIS-DSP biquad cascade filter
    arm_biquad_cascade_df1_f32(&context->postfilter, input, output, length);
#else
    // Fallback: simple high-pass filter
    float32_t prev = context->postfilt_state[0];
    for (uint32_t i = 0; i < length; i++) {
        output[i] = input[i] - 0.95f * prev;
        prev = input[i];
    }
    context->postfilt_state[0] = prev;
#endif

    return true;
}

void melp_dsp_float_to_q15(const float32_t *input, q15_t *output, uint32_t length)
{
#if MELP_USE_CMSIS_DSP
    arm_float_to_q15(input, output, length);
#else
    for (uint32_t i = 0; i < length; i++) {
        float32_t temp = input[i] * MELP_Q15_SCALE;
        if (temp > 32767.0f) temp = 32767.0f;
        if (temp < -32768.0f) temp = -32768.0f;
        output[i] = (q15_t)temp;
    }
#endif
}

void melp_dsp_q15_to_float(const q15_t *input, float32_t *output, uint32_t length)
{
#if MELP_USE_CMSIS_DSP
    arm_q15_to_float(input, output, length);
#else
    for (uint32_t i = 0; i < length; i++) {
        output[i] = (float32_t)input[i] / MELP_Q15_SCALE;
    }
#endif
}

float32_t melp_dsp_dot_product(const float32_t *vec_a, const float32_t *vec_b, uint32_t length)
{
    float32_t result;
    
#if MELP_USE_CMSIS_DSP
    arm_dot_prod_f32(vec_a, vec_b, length, &result);
#else
    result = 0.0f;
    for (uint32_t i = 0; i < length; i++) {
        result += vec_a[i] * vec_b[i];
    }
#endif
    
    return result;
}

void melp_dsp_vector_scale(const float32_t *input, float32_t scale, float32_t *output, uint32_t length)
{
#if MELP_USE_CMSIS_DSP
    arm_scale_f32(input, scale, output, length);
#else
    for (uint32_t i = 0; i < length; i++) {
        output[i] = input[i] * scale;
    }
#endif
}
