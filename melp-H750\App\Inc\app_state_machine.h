/* SPDX-License-Identifier: MIT */
/**
 * @file app_state_machine.h
 * @brief Application state machine interface
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

#ifndef APP_STATE_MACHINE_H
#define APP_STATE_MACHINE_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "../../inc/project_config.h"

/* ========================================================================== */
/*                           TYPE DEFINITIONS                                */
/* ========================================================================== */

/**
 * @brief Application states
 */
typedef enum {
    APP_SM_STATE_INIT = 0,
    APP_SM_STATE_IDLE,
    APP_SM_STATE_RECORDING,
    APP_SM_STATE_ENCODING,
    APP_SM_STATE_TRANSMITTING,
    APP_SM_STATE_RECEIVING,
    APP_SM_STATE_DECODING,
    APP_SM_STATE_PLAYING,
    APP_SM_STATE_ERROR,
    APP_SM_STATE_RECOVERY,
    APP_SM_STATE_SHUTDOWN,
    APP_SM_STATE_COUNT
} app_sm_state_t;

/**
 * @brief Application events
 */
typedef enum {
    APP_SM_EVENT_NONE = 0,
    APP_SM_EVENT_INIT_COMPLETE,
    APP_SM_EVENT_BUTTON_PRESSED,
    APP_SM_EVENT_BUTTON_RELEASED,
    APP_SM_EVENT_AUDIO_FRAME_READY,
    APP_SM_EVENT_FRAME_ENCODED,
    APP_SM_EVENT_FRAME_TRANSMITTED,
    APP_SM_EVENT_FRAME_RECEIVED,
    APP_SM_EVENT_FRAME_DECODED,
    APP_SM_EVENT_PLAYBACK_COMPLETE,
    APP_SM_EVENT_ERROR_OCCURRED,
    APP_SM_EVENT_RECOVERY_COMPLETE,
    APP_SM_EVENT_TIMEOUT,
    APP_SM_EVENT_SHUTDOWN_REQUEST,
    APP_SM_EVENT_COUNT
} app_sm_event_t;

/**
 * @brief Error codes
 */
typedef enum {
    APP_SM_ERROR_NONE = 0,
    APP_SM_ERROR_HARDWARE_FAILURE,
    APP_SM_ERROR_MEMORY_ALLOCATION,
    APP_SM_ERROR_CODEC_FAILURE,
    APP_SM_ERROR_COMMUNICATION_FAILURE,
    APP_SM_ERROR_TIMEOUT,
    APP_SM_ERROR_INVALID_STATE,
    APP_SM_ERROR_INVALID_EVENT,
    APP_SM_ERROR_BUFFER_OVERFLOW,
    APP_SM_ERROR_BUFFER_UNDERFLOW,
    APP_SM_ERROR_COUNT
} app_sm_error_t;

/**
 * @brief State machine context
 */
typedef struct {
    app_sm_state_t current_state;
    app_sm_state_t previous_state;
    app_sm_event_t last_event;
    app_sm_error_t last_error;
    
    // Timing
    uint32_t state_entry_time;
    uint32_t state_duration;
    uint32_t timeout_value;
    
    // Counters
    uint32_t error_count;
    uint32_t recovery_attempts;
    uint32_t state_transitions;
    
    // Flags
    bool timeout_enabled;
    bool error_recovery_enabled;
    bool debug_mode;
    
    // Statistics
    uint32_t time_in_state[APP_SM_STATE_COUNT];
    uint32_t event_count[APP_SM_EVENT_COUNT];
    uint32_t error_count_by_type[APP_SM_ERROR_COUNT];
    
    bool initialized;
} app_sm_context_t;

/**
 * @brief State machine callbacks
 */
typedef struct {
    void (*on_state_entry)(app_sm_state_t state);
    void (*on_state_exit)(app_sm_state_t state);
    void (*on_state_transition)(app_sm_state_t from, app_sm_state_t to, app_sm_event_t event);
    void (*on_error)(app_sm_error_t error, app_sm_state_t state);
    void (*on_timeout)(app_sm_state_t state, uint32_t duration);
} app_sm_callbacks_t;

/* ========================================================================== */
/*                           FUNCTION DECLARATIONS                           */
/* ========================================================================== */

/**
 * @brief Initialize state machine
 * @param context Pointer to state machine context
 * @param callbacks Pointer to callback functions
 * @return true if successful, false otherwise
 */
bool app_sm_init(app_sm_context_t *context, const app_sm_callbacks_t *callbacks);

/**
 * @brief Deinitialize state machine
 * @param context Pointer to state machine context
 * @return true if successful, false otherwise
 */
bool app_sm_deinit(app_sm_context_t *context);

/**
 * @brief Process an event
 * @param context Pointer to state machine context
 * @param event Event to process
 * @return true if event was processed, false otherwise
 */
bool app_sm_process_event(app_sm_context_t *context, app_sm_event_t event);

/**
 * @brief Report an error
 * @param context Pointer to state machine context
 * @param error Error code
 * @return true if error was processed, false otherwise
 */
bool app_sm_report_error(app_sm_context_t *context, app_sm_error_t error);

/**
 * @brief Update state machine (call periodically)
 * @param context Pointer to state machine context
 * @param current_time Current system time in milliseconds
 * @return true if successful, false otherwise
 */
bool app_sm_update(app_sm_context_t *context, uint32_t current_time);

/**
 * @brief Get current state
 * @param context Pointer to state machine context
 * @return Current state
 */
app_sm_state_t app_sm_get_state(const app_sm_context_t *context);

/**
 * @brief Get state name string
 * @param state State to get name for
 * @return State name string
 */
const char* app_sm_get_state_name(app_sm_state_t state);

/**
 * @brief Get event name string
 * @param event Event to get name for
 * @return Event name string
 */
const char* app_sm_get_event_name(app_sm_event_t event);

/**
 * @brief Get error name string
 * @param error Error to get name for
 * @return Error name string
 */
const char* app_sm_get_error_name(app_sm_error_t error);

/**
 * @brief Get state machine statistics
 * @param context Pointer to state machine context
 * @param total_transitions Output: total state transitions
 * @param total_errors Output: total errors
 * @param uptime_ms Output: total uptime in milliseconds
 * @return true if successful, false otherwise
 */
bool app_sm_get_statistics(const app_sm_context_t *context,
                           uint32_t *total_transitions,
                           uint32_t *total_errors,
                           uint32_t *uptime_ms);

/**
 * @brief Reset state machine statistics
 * @param context Pointer to state machine context
 * @return true if successful, false otherwise
 */
bool app_sm_reset_statistics(app_sm_context_t *context);

/**
 * @brief Enable/disable debug mode
 * @param context Pointer to state machine context
 * @param enable true to enable debug mode, false to disable
 * @return true if successful, false otherwise
 */
bool app_sm_set_debug_mode(app_sm_context_t *context, bool enable);

/**
 * @brief Force state transition (for testing/recovery)
 * @param context Pointer to state machine context
 * @param new_state State to transition to
 * @return true if successful, false otherwise
 */
bool app_sm_force_state(app_sm_context_t *context, app_sm_state_t new_state);

#ifdef __cplusplus
}
#endif

#endif /* APP_STATE_MACHINE_H */
