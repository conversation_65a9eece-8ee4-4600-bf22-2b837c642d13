/* SPDX-License-Identifier: MIT */
/**
 * @file melp_dsp.h
 * @brief MELP DSP optimized functions using CMSIS-DSP
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

#ifndef MELP_DSP_H
#define MELP_DSP_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "project_config.h"

// Include CMSIS-DSP if available
#ifdef ARM_MATH_CM7
#include "arm_math.h"
#define MELP_USE_CMSIS_DSP 1
#else
// Define CMSIS-DSP types if not available
typedef float float32_t;
typedef int16_t q15_t;
#define MELP_USE_CMSIS_DSP 0
#endif

/* ========================================================================== */
/*                           DSP CONSTANTS                                   */
/* ========================================================================== */

/** Fixed-point scaling factors */
#define MELP_Q15_SCALE              32768.0f
#define MELP_Q31_SCALE              **********.0f

/** FFT sizes for spectral analysis */
#define MELP_FFT_SIZE               256
#define MELP_FFT_SIZE_LOG2          8

/* ========================================================================== */
/*                           TYPE DEFINITIONS                                */
/* ========================================================================== */

/**
 * @brief DSP context for MELP processing
 */
typedef struct {
    // CMSIS-DSP instances
#if MELP_USE_CMSIS_DSP
    arm_rfft_fast_instance_f32 rfft_instance;
    arm_cfft_radix4_instance_f32 cfft_instance;
    arm_fir_instance_f32 preemph_filter;
    arm_biquad_casd_df1_inst_f32 postfilter;
#endif
    
    // Working buffers
    float32_t fft_buffer[MELP_FFT_SIZE * 2];
    float32_t window_buffer[AUDIO_FRAME_LENGTH_SAMPLES];
    float32_t autocorr_buffer[MELP_LPC_ORDER + 1];
    
    // Filter coefficients
    float32_t preemph_coeffs[2];
    float32_t preemph_state[1];
    float32_t postfilt_coeffs[10];
    float32_t postfilt_state[8];
    
    bool initialized;
} melp_dsp_context_t;

/* ========================================================================== */
/*                           FUNCTION DECLARATIONS                           */
/* ========================================================================== */

/**
 * @brief Initialize MELP DSP context
 * @param context Pointer to DSP context
 * @return true if successful, false otherwise
 */
bool melp_dsp_init(melp_dsp_context_t *context);

/**
 * @brief Deinitialize MELP DSP context
 * @param context Pointer to DSP context
 * @return true if successful, false otherwise
 */
bool melp_dsp_deinit(melp_dsp_context_t *context);

/**
 * @brief Optimized pre-emphasis filtering
 * @param context DSP context
 * @param input Input signal
 * @param output Output signal
 * @param length Signal length
 * @return true if successful, false otherwise
 */
bool melp_dsp_preemphasis(melp_dsp_context_t *context,
                          const float32_t *input,
                          float32_t *output,
                          uint32_t length);

/**
 * @brief Optimized windowing function
 * @param input Input signal
 * @param window Window coefficients
 * @param output Output windowed signal
 * @param length Signal length
 * @return true if successful, false otherwise
 */
bool melp_dsp_windowing(const float32_t *input,
                        const float32_t *window,
                        float32_t *output,
                        uint32_t length);

/**
 * @brief Optimized autocorrelation computation
 * @param context DSP context
 * @param input Input signal
 * @param autocorr Output autocorrelation
 * @param length Signal length
 * @param order Autocorrelation order
 * @return true if successful, false otherwise
 */
bool melp_dsp_autocorrelation(melp_dsp_context_t *context,
                              const float32_t *input,
                              float32_t *autocorr,
                              uint32_t length,
                              uint32_t order);

/**
 * @brief Optimized Levinson-Durbin algorithm
 * @param autocorr Input autocorrelation
 * @param lpc_coeffs Output LPC coefficients
 * @param order LPC order
 * @param error Output prediction error
 * @return true if successful, false otherwise
 */
bool melp_dsp_levinson_durbin(const float32_t *autocorr,
                              float32_t *lpc_coeffs,
                              uint32_t order,
                              float32_t *error);

/**
 * @brief Optimized LPC synthesis filtering
 * @param lpc_coeffs LPC coefficients
 * @param excitation Input excitation
 * @param output Output synthesized signal
 * @param length Signal length
 * @param order LPC order
 * @param memory Filter memory
 * @return true if successful, false otherwise
 */
bool melp_dsp_lpc_synthesis(const float32_t *lpc_coeffs,
                            const float32_t *excitation,
                            float32_t *output,
                            uint32_t length,
                            uint32_t order,
                            float32_t *memory);

/**
 * @brief Optimized pitch estimation using autocorrelation
 * @param context DSP context
 * @param input Input signal
 * @param pitch_min Minimum pitch lag
 * @param pitch_max Maximum pitch lag
 * @param pitch Output pitch value
 * @param correlation Output correlation value
 * @return true if successful, false otherwise
 */
bool melp_dsp_pitch_estimation(melp_dsp_context_t *context,
                               const float32_t *input,
                               uint32_t pitch_min,
                               uint32_t pitch_max,
                               float32_t *pitch,
                               float32_t *correlation);

/**
 * @brief Optimized spectral analysis using FFT
 * @param context DSP context
 * @param input Input signal
 * @param magnitude Output magnitude spectrum
 * @param phase Output phase spectrum
 * @param length Signal length (must be power of 2)
 * @return true if successful, false otherwise
 */
bool melp_dsp_spectral_analysis(melp_dsp_context_t *context,
                                const float32_t *input,
                                float32_t *magnitude,
                                float32_t *phase,
                                uint32_t length);

/**
 * @brief Optimized post-filtering
 * @param context DSP context
 * @param input Input signal
 * @param lpc_coeffs LPC coefficients
 * @param output Output filtered signal
 * @param length Signal length
 * @return true if successful, false otherwise
 */
bool melp_dsp_post_filter(melp_dsp_context_t *context,
                          const float32_t *input,
                          const float32_t *lpc_coeffs,
                          float32_t *output,
                          uint32_t length);

/**
 * @brief Convert float to Q15 fixed-point
 * @param input Input float array
 * @param output Output Q15 array
 * @param length Array length
 */
void melp_dsp_float_to_q15(const float32_t *input, q15_t *output, uint32_t length);

/**
 * @brief Convert Q15 fixed-point to float
 * @param input Input Q15 array
 * @param output Output float array
 * @param length Array length
 */
void melp_dsp_q15_to_float(const q15_t *input, float32_t *output, uint32_t length);

/**
 * @brief Vector dot product optimized
 * @param vec_a First vector
 * @param vec_b Second vector
 * @param length Vector length
 * @return Dot product result
 */
float32_t melp_dsp_dot_product(const float32_t *vec_a, const float32_t *vec_b, uint32_t length);

/**
 * @brief Vector scaling optimized
 * @param input Input vector
 * @param scale Scale factor
 * @param output Output vector
 * @param length Vector length
 */
void melp_dsp_vector_scale(const float32_t *input, float32_t scale, float32_t *output, uint32_t length);

#ifdef __cplusplus
}
#endif

#endif /* MELP_DSP_H */
