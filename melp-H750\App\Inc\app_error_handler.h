/* SPDX-License-Identifier: MIT */
/**
 * @file app_error_handler.h
 * @brief Application error handling and recovery system
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

#ifndef APP_ERROR_HANDLER_H
#define APP_ERROR_HANDLER_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "../../inc/project_config.h"

/* ========================================================================== */
/*                           ERROR DEFINITIONS                               */
/* ========================================================================== */

/**
 * @brief System error codes
 */
typedef enum {
    APP_ERROR_NONE = 0x0000,
    
    // Hardware errors (0x1000-0x1FFF)
    APP_ERROR_ADC_FAILURE = 0x1001,
    APP_ERROR_DAC_FAILURE = 0x1002,
    APP_ERROR_UART_FAILURE = 0x1003,
    APP_ERROR_GPIO_FAILURE = 0x1004,
    APP_ERROR_DMA_FAILURE = 0x1005,
    APP_ERROR_CLOCK_FAILURE = 0x1006,
    
    // Memory errors (0x2000-0x2FFF)
    APP_ERROR_MEMORY_ALLOCATION = 0x2001,
    APP_ERROR_BUFFER_OVERFLOW = 0x2002,
    APP_ERROR_BUFFER_UNDERFLOW = 0x2003,
    APP_ERROR_STACK_OVERFLOW = 0x2004,
    APP_ERROR_HEAP_CORRUPTION = 0x2005,
    
    // Algorithm errors (0x3000-0x3FFF)
    APP_ERROR_MELP_ENCODER_FAILURE = 0x3001,
    APP_ERROR_MELP_DECODER_FAILURE = 0x3002,
    APP_ERROR_LPC_ANALYSIS_FAILURE = 0x3003,
    APP_ERROR_PITCH_ESTIMATION_FAILURE = 0x3004,
    APP_ERROR_QUANTIZATION_FAILURE = 0x3005,
    
    // Communication errors (0x4000-0x4FFF)
    APP_ERROR_COMM_TIMEOUT = 0x4001,
    APP_ERROR_COMM_CRC_ERROR = 0x4002,
    APP_ERROR_COMM_SEQUENCE_ERROR = 0x4003,
    APP_ERROR_COMM_PROTOCOL_ERROR = 0x4004,
    APP_ERROR_COMM_BUFFER_FULL = 0x4005,
    
    // System errors (0x5000-0x5FFF)
    APP_ERROR_WATCHDOG_TIMEOUT = 0x5001,
    APP_ERROR_SYSTEM_OVERLOAD = 0x5002,
    APP_ERROR_POWER_FAILURE = 0x5003,
    APP_ERROR_TEMPERATURE_CRITICAL = 0x5004,
    APP_ERROR_INVALID_STATE = 0x5005,
    
    // Application errors (0x6000-0x6FFF)
    APP_ERROR_INVALID_PARAMETER = 0x6001,
    APP_ERROR_OPERATION_TIMEOUT = 0x6002,
    APP_ERROR_RESOURCE_BUSY = 0x6003,
    APP_ERROR_INITIALIZATION_FAILED = 0x6004,
    APP_ERROR_CONFIGURATION_ERROR = 0x6005
} app_error_code_t;

/**
 * @brief Error severity levels
 */
typedef enum {
    APP_ERROR_SEVERITY_INFO = 0,
    APP_ERROR_SEVERITY_WARNING,
    APP_ERROR_SEVERITY_ERROR,
    APP_ERROR_SEVERITY_CRITICAL,
    APP_ERROR_SEVERITY_FATAL
} app_error_severity_t;

/**
 * @brief Recovery actions
 */
typedef enum {
    APP_RECOVERY_NONE = 0,
    APP_RECOVERY_RETRY,
    APP_RECOVERY_RESET_MODULE,
    APP_RECOVERY_RESTART_SYSTEM,
    APP_RECOVERY_SAFE_MODE,
    APP_RECOVERY_SHUTDOWN
} app_recovery_action_t;

/**
 * @brief Error record structure
 */
typedef struct {
    app_error_code_t error_code;
    app_error_severity_t severity;
    uint32_t timestamp;
    uint32_t line_number;
    const char* file_name;
    const char* function_name;
    uint32_t context_data;
} app_error_record_t;

/**
 * @brief Error handler context
 */
typedef struct {
    app_error_record_t error_log[32];  // Circular buffer
    uint32_t error_count;
    uint32_t log_index;
    uint32_t recovery_attempts;
    bool recovery_in_progress;
    bool initialized;
} app_error_handler_context_t;

/**
 * @brief Error handler callbacks
 */
typedef struct {
    void (*on_error_occurred)(const app_error_record_t* error);
    void (*on_recovery_started)(app_recovery_action_t action);
    void (*on_recovery_completed)(bool success);
    void (*on_critical_error)(app_error_code_t error_code);
} app_error_handler_callbacks_t;

/* ========================================================================== */
/*                           FUNCTION DECLARATIONS                           */
/* ========================================================================== */

/**
 * @brief Initialize error handler
 * @param context Pointer to error handler context
 * @param callbacks Pointer to callback functions
 * @return true if successful, false otherwise
 */
bool app_error_handler_init(app_error_handler_context_t* context,
                           const app_error_handler_callbacks_t* callbacks);

/**
 * @brief Report an error
 * @param context Pointer to error handler context
 * @param error_code Error code
 * @param severity Error severity
 * @param file_name Source file name
 * @param line_number Source line number
 * @param function_name Function name
 * @param context_data Additional context data
 * @return Recovery action to take
 */
app_recovery_action_t app_error_handler_report(app_error_handler_context_t* context,
                                              app_error_code_t error_code,
                                              app_error_severity_t severity,
                                              const char* file_name,
                                              uint32_t line_number,
                                              const char* function_name,
                                              uint32_t context_data);

/**
 * @brief Execute recovery action
 * @param context Pointer to error handler context
 * @param action Recovery action to execute
 * @return true if recovery was successful, false otherwise
 */
bool app_error_handler_recover(app_error_handler_context_t* context,
                              app_recovery_action_t action);

/**
 * @brief Get error statistics
 * @param context Pointer to error handler context
 * @param total_errors Output: total error count
 * @param critical_errors Output: critical error count
 * @param recovery_success_rate Output: recovery success rate (0-100)
 * @return true if successful, false otherwise
 */
bool app_error_handler_get_stats(const app_error_handler_context_t* context,
                                 uint32_t* total_errors,
                                 uint32_t* critical_errors,
                                 uint32_t* recovery_success_rate);

/**
 * @brief Get error name string
 * @param error_code Error code
 * @return Error name string
 */
const char* app_error_get_name(app_error_code_t error_code);

/**
 * @brief Get severity name string
 * @param severity Error severity
 * @return Severity name string
 */
const char* app_error_get_severity_name(app_error_severity_t severity);

/* ========================================================================== */
/*                           CONVENIENCE MACROS                              */
/* ========================================================================== */

#define APP_ERROR_REPORT(context, code, severity, data) \
    app_error_handler_report(context, code, severity, __FILE__, __LINE__, __FUNCTION__, data)

#define APP_ERROR_REPORT_INFO(context, code, data) \
    APP_ERROR_REPORT(context, code, APP_ERROR_SEVERITY_INFO, data)

#define APP_ERROR_REPORT_WARNING(context, code, data) \
    APP_ERROR_REPORT(context, code, APP_ERROR_SEVERITY_WARNING, data)

#define APP_ERROR_REPORT_ERROR(context, code, data) \
    APP_ERROR_REPORT(context, code, APP_ERROR_SEVERITY_ERROR, data)

#define APP_ERROR_REPORT_CRITICAL(context, code, data) \
    APP_ERROR_REPORT(context, code, APP_ERROR_SEVERITY_CRITICAL, data)

#define APP_ERROR_REPORT_FATAL(context, code, data) \
    APP_ERROR_REPORT(context, code, APP_ERROR_SEVERITY_FATAL, data)

#ifdef __cplusplus
}
#endif

#endif /* APP_ERROR_HANDLER_H */
