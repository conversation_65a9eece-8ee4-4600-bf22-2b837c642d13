/* SPDX-License-Identifier: MIT */
/**
 * @file melp_encoder.c
 * @brief MELP (Mixed Excitation Linear Prediction) Encoder Implementation
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

#include "../inc/melp_encoder.h"
#include <string.h>
#include <math.h>

/* ========================================================================== */
/*                           PRIVATE CONSTANTS                               */
/* ========================================================================== */

#define MELP_VERSION_STRING         "MELP Encoder v1.0.0"
#define MELP_PI                     3.14159265358979323846f
#define MELP_EPSILON                1e-10f

/* ========================================================================== */
/*                           PRIVATE VARIABLES                               */
/* ========================================================================== */

/** Hamming window for LPC analysis */
static float hamming_window[MELP_FRAME_SIZE];
static bool window_initialized = false;

/* ========================================================================== */
/*                           PRIVATE FUNCTION DECLARATIONS                   */
/* ========================================================================== */

static void melp_init_hamming_window(void);
static void melp_preemphasis(const int16_t *input, float *output, 
                             float *mem, uint32_t length);
static float melp_autocorrelation(const float *input, float *autocorr, 
                                  uint32_t length, uint32_t order);
static bool melp_levinson_durbin(const float *autocorr, float *lpc_coeffs,
                                 uint32_t order);

/* ========================================================================== */
/*                           PUBLIC FUNCTION IMPLEMENTATIONS                 */
/* ========================================================================== */

bool melp_encoder_init(melp_encoder_state_t *state)
{
    if (state == NULL) {
        return false;
    }

    // Clear all memory
    memset(state, 0, sizeof(melp_encoder_state_t));

    // Initialize Hamming window if not already done
    if (!window_initialized) {
        melp_init_hamming_window();
        window_initialized = true;
    }

    state->initialized = true;
    return true;
}

bool melp_encoder_deinit(melp_encoder_state_t *state)
{
    if (state == NULL) {
        return false;
    }

    memset(state, 0, sizeof(melp_encoder_state_t));
    return true;
}

bool melp_encoder_encode_frame(melp_encoder_state_t *state,
                               const int16_t *input_samples,
                               melp_encoded_frame_t *encoded_frame)
{
    if (state == NULL || input_samples == NULL || encoded_frame == NULL) {
        return false;
    }

    if (!state->initialized) {
        return false;
    }

    float speech_buffer[MELP_FRAME_SIZE];
    melp_params_t params;

    // Pre-emphasis filtering
    melp_preemphasis(input_samples, speech_buffer, &state->preemph_mem, MELP_FRAME_SIZE);

    // LPC analysis
    if (!melp_lpc_analysis(speech_buffer, params.lpc_coeffs, &params.gain)) {
        return false;
    }

    // Convert LPC to LSF
    if (!melp_lpc_to_lsf(params.lpc_coeffs, params.lsf_coeffs)) {
        return false;
    }

    // Pitch estimation
    if (!melp_pitch_estimation(speech_buffer, &params.pitch)) {
        return false;
    }

    // Voicing analysis
    if (!melp_voicing_analysis(speech_buffer, params.pitch, params.voicing)) {
        return false;
    }

    // Set aperiodic flag based on voicing
    float avg_voicing = 0.0f;
    for (int i = 0; i < MELP_NUM_BANDS; i++) {
        avg_voicing += params.voicing[i];
    }
    avg_voicing /= MELP_NUM_BANDS;
    params.aperiodic_flag = (avg_voicing < MELP_VOICING_THRESH);

    // Quantize parameters
    if (!melp_quantize_parameters(&params, encoded_frame)) {
        return false;
    }

    // Update state
    state->pitch_mem = params.pitch;
    state->gain_mem = params.gain;
    state->frame_count++;

    return true;
}

uint8_t melp_encoder_pack_frame(const melp_encoded_frame_t *encoded_frame,
                                uint8_t *bit_stream)
{
    if (encoded_frame == NULL || bit_stream == NULL) {
        return 0;
    }

    // Simple bit packing for 14-bit frame (2 bytes)
    // This is a simplified implementation - actual MELP uses more complex packing
    uint16_t packed_bits = 0;
    
    // Pack LSF indices (6 bits)
    packed_bits |= (encoded_frame->lsf_indices[0] & 0x3F);
    
    // Pack pitch index (4 bits) 
    packed_bits |= ((encoded_frame->pitch_index & 0x0F) << 6);
    
    // Pack gain index (3 bits)
    packed_bits |= ((encoded_frame->gain_index & 0x07) << 10);
    
    // Pack sync bit (1 bit)
    packed_bits |= ((encoded_frame->sync_bit & 0x01) << 13);

    // Store as little-endian
    bit_stream[0] = (uint8_t)(packed_bits & 0xFF);
    bit_stream[1] = (uint8_t)((packed_bits >> 8) & 0xFF);

    return 14; // 14 bits packed
}

const char* melp_encoder_get_version(void)
{
    return MELP_VERSION_STRING;
}

/* ========================================================================== */
/*                           INTERNAL FUNCTION IMPLEMENTATIONS               */
/* ========================================================================== */

bool melp_lpc_analysis(const float *input, float *lpc_coeffs, float *gain)
{
    if (input == NULL || lpc_coeffs == NULL || gain == NULL) {
        return false;
    }

    float windowed_speech[MELP_FRAME_SIZE];
    float autocorr[MELP_LPC_ORDER_ALG + 1];

    // Apply Hamming window
    for (uint32_t i = 0; i < MELP_FRAME_SIZE; i++) {
        windowed_speech[i] = input[i] * hamming_window[i];
    }

    // Compute autocorrelation
    *gain = melp_autocorrelation(windowed_speech, autocorr, MELP_FRAME_SIZE, MELP_LPC_ORDER_ALG);

    // Levinson-Durbin algorithm
    if (!melp_levinson_durbin(autocorr, lpc_coeffs, MELP_LPC_ORDER_ALG)) {
        return false;
    }

    return true;
}

bool melp_lpc_to_lsf(const float *lpc_coeffs, float *lsf_coeffs)
{
    // Simplified LSF conversion - actual implementation would use
    // Chebyshev polynomial root finding
    if (lpc_coeffs == NULL || lsf_coeffs == NULL) {
        return false;
    }

    // Placeholder implementation - convert LPC to approximate LSF
    for (int i = 0; i < MELP_LPC_ORDER_ALG; i++) {
        lsf_coeffs[i] = (float)(i + 1) * MELP_PI / (MELP_LPC_ORDER_ALG + 1);
    }

    return true;
}

bool melp_pitch_estimation(const float *input, float *pitch)
{
    if (input == NULL || pitch == NULL) {
        return false;
    }

    // Simplified pitch estimation using autocorrelation
    float max_corr = 0.0f;
    int best_lag = MELP_PITCH_MIN;

    for (int lag = MELP_PITCH_MIN; lag <= MELP_PITCH_MAX; lag++) {
        float corr = 0.0f;
        for (int i = 0; i < MELP_FRAME_SIZE - lag; i++) {
            corr += input[i] * input[i + lag];
        }
        
        if (corr > max_corr) {
            max_corr = corr;
            best_lag = lag;
        }
    }

    *pitch = (float)best_lag;
    return true;
}

bool melp_voicing_analysis(const float *input, float pitch, float *voicing)
{
    if (input == NULL || voicing == NULL) {
        return false;
    }

    // Simplified voicing analysis - actual implementation would use
    // normalized correlation and spectral analysis
    float energy = 0.0f;
    for (uint32_t i = 0; i < MELP_FRAME_SIZE; i++) {
        energy += input[i] * input[i];
    }

    // Simple voicing decision based on energy and pitch
    float voicing_strength = (energy > 1000.0f && pitch > 0.0f) ? 0.8f : 0.2f;
    
    for (int i = 0; i < MELP_NUM_BANDS; i++) {
        voicing[i] = voicing_strength;
    }

    return true;
}

bool melp_quantize_parameters(const melp_params_t *params,
                              melp_encoded_frame_t *encoded_frame)
{
    if (params == NULL || encoded_frame == NULL) {
        return false;
    }

    // Simplified quantization - actual MELP uses vector quantization
    // and sophisticated codebooks
    
    // Quantize LSF (6 bits total)
    encoded_frame->lsf_indices[0] = 32; // Placeholder
    
    // Quantize pitch (4 bits: 16 levels)
    int pitch_index = (int)((params->pitch - MELP_PITCH_MIN) * 15.0f / 
                           (MELP_PITCH_MAX - MELP_PITCH_MIN));
    encoded_frame->pitch_index = (uint8_t)(pitch_index & 0x0F);
    
    // Quantize gain (3 bits: 8 levels)
    int gain_index = (int)(params->gain * 7.0f / 32768.0f);
    encoded_frame->gain_index = (uint8_t)(gain_index & 0x07);
    
    // Quantize voicing (implicit in other parameters)
    encoded_frame->voicing_index = 0;
    
    // Sync bit
    encoded_frame->sync_bit = 1;

    return true;
}

/* ========================================================================== */
/*                           PRIVATE FUNCTION IMPLEMENTATIONS                */
/* ========================================================================== */

static void melp_init_hamming_window(void)
{
    for (uint32_t i = 0; i < MELP_FRAME_SIZE; i++) {
        hamming_window[i] = 0.54f - 0.46f * cosf(2.0f * MELP_PI * i / (MELP_FRAME_SIZE - 1));
    }
}

static void melp_preemphasis(const int16_t *input, float *output, 
                             float *mem, uint32_t length)
{
    float prev = *mem;
    
    for (uint32_t i = 0; i < length; i++) {
        float current = (float)input[i];
        output[i] = current - MELP_PREEMPH_COEFF * prev;
        prev = current;
    }
    
    *mem = prev;
}

static float melp_autocorrelation(const float *input, float *autocorr, 
                                  uint32_t length, uint32_t order)
{
    // Compute autocorrelation coefficients
    for (uint32_t k = 0; k <= order; k++) {
        autocorr[k] = 0.0f;
        for (uint32_t i = 0; i < length - k; i++) {
            autocorr[k] += input[i] * input[i + k];
        }
    }
    
    return sqrtf(autocorr[0] / length); // Return RMS gain
}

static bool melp_levinson_durbin(const float *autocorr, float *lpc_coeffs, 
                                 uint32_t order)
{
    float alpha[MELP_LPC_ORDER_ALG + 1][MELP_LPC_ORDER_ALG + 1];
    float error = autocorr[0];
    
    if (error <= MELP_EPSILON) {
        return false;
    }
    
    // Levinson-Durbin recursion
    for (uint32_t i = 1; i <= order; i++) {
        float sum = 0.0f;
        for (uint32_t j = 1; j < i; j++) {
            sum += alpha[i-1][j] * autocorr[i-j];
        }
        
        float k = -(autocorr[i] + sum) / error;
        alpha[i][i] = k;
        
        for (uint32_t j = 1; j < i; j++) {
            alpha[i][j] = alpha[i-1][j] + k * alpha[i-1][i-j];
        }
        
        error *= (1.0f - k * k);
        if (error <= MELP_EPSILON) {
            return false;
        }
    }
    
    // Copy final coefficients
    lpc_coeffs[0] = 1.0f;
    for (uint32_t i = 1; i <= order; i++) {
        lpc_coeffs[i] = alpha[order][i];
    }
    
    return true;
}
