/* SPDX-License-Identifier: MIT */
/**
 * @file app_main.h
 * @brief Main application interface
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

#ifndef APP_MAIN_H
#define APP_MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

/* ========================================================================== */
/*                           TYPE DEFINITIONS                                */
/* ========================================================================== */

/**
 * @brief Application states
 */
typedef enum {
    APP_STATE_IDLE = 0,
    APP_STATE_RECORDING,
    APP_STATE_PLAYING,
    APP_STATE_ERROR
} app_state_t;

/* ========================================================================== */
/*                           FUNCTION DECLARATIONS                           */
/* ========================================================================== */

/**
 * @brief Initialize application
 * @return true if successful, false otherwise
 */
bool app_init(void);

/**
 * @brief Main application loop (call from main)
 */
void app_run(void);

/**
 * @brief Get current application state
 * @return Current application state
 */
app_state_t app_get_state(void);

/**
 * @brief Handle system tick (call from SysTick handler)
 */
void app_systick_handler(void);

#ifdef __cplusplus
}
#endif

#endif /* APP_MAIN_H */
