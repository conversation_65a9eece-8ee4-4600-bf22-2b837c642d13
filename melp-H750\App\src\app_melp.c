/* SPDX-License-Identifier: MIT */
/**
 * @file app_melp.c
 * @brief MELP application implementation
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

#include "app_melp.h"
#include <string.h>

/* ========================================================================== */
/*                           PRIVATE VARIABLES                               */
/* ========================================================================== */

static app_melp_callbacks_t app_callbacks;
static bool callbacks_initialized = false;

/* ========================================================================== */
/*                           PRIVATE FUNCTION DECLARATIONS                   */
/* ========================================================================== */

static void app_melp_change_state(app_melp_context_t *context, app_melp_state_t new_state);
static bool app_melp_encode_frame(app_melp_context_t *context);
static bool app_melp_decode_frame(app_melp_context_t *context, const uint8_t *frame_data);

/* ========================================================================== */
/*                           PUBLIC FUNCTION IMPLEMENTATIONS                 */
/* ========================================================================== */

bool app_melp_init(app_melp_context_t *context, const app_melp_callbacks_t *callbacks)
{
    if (context == NULL) {
        return false;
    }

    // Clear context
    memset(context, 0, sizeof(app_melp_context_t));

    // Initialize MELP encoder
    if (!melp_encoder_init(&context->encoder_state)) {
        return false;
    }

    // Initialize MELP decoder
    if (!melp_decoder_init(&context->decoder_state)) {
        melp_encoder_deinit(&context->encoder_state);
        return false;
    }

    // Store callbacks
    if (callbacks != NULL) {
        memcpy(&app_callbacks, callbacks, sizeof(app_melp_callbacks_t));
        callbacks_initialized = true;
    }

    context->current_state = APP_MELP_STATE_IDLE;
    context->initialized = true;

    return true;
}

bool app_melp_deinit(app_melp_context_t *context)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    // Deinitialize MELP components
    melp_encoder_deinit(&context->encoder_state);
    melp_decoder_deinit(&context->decoder_state);

    // Clear context
    memset(context, 0, sizeof(app_melp_context_t));
    callbacks_initialized = false;

    return true;
}

bool app_melp_process_input(app_melp_context_t *context, 
                            const int16_t *samples, 
                            uint32_t sample_count)
{
    if (context == NULL || !context->initialized || samples == NULL || sample_count == 0) {
        return false;
    }

    // Check if we're in recording state
    if (context->current_state != APP_MELP_STATE_RECORDING) {
        return false;
    }

    // Add samples to input buffer
    uint32_t samples_to_copy = sample_count;
    uint32_t buffer_space = AUDIO_INPUT_BUFFER_SIZE - context->input_samples_count;
    
    if (samples_to_copy > buffer_space) {
        samples_to_copy = buffer_space;
    }

    memcpy(&context->input_buffer[context->input_samples_count], 
           samples, 
           samples_to_copy * sizeof(int16_t));
    
    context->input_samples_count += samples_to_copy;

    // Check if we have enough samples for a frame
    if (context->input_samples_count >= MELP_FRAME_SIZE) {
        app_melp_change_state(context, APP_MELP_STATE_ENCODING);
        
        if (app_melp_encode_frame(context)) {
            context->frames_encoded++;
            app_melp_change_state(context, APP_MELP_STATE_TRANSMITTING);
        } else {
            context->frame_errors++;
            app_melp_change_state(context, APP_MELP_STATE_ERROR);
            return false;
        }

        // Shift remaining samples
        uint32_t remaining_samples = context->input_samples_count - MELP_FRAME_SIZE;
        if (remaining_samples > 0) {
            memmove(context->input_buffer, 
                    &context->input_buffer[MELP_FRAME_SIZE],
                    remaining_samples * sizeof(int16_t));
        }
        context->input_samples_count = remaining_samples;
        
        app_melp_change_state(context, APP_MELP_STATE_RECORDING);
    }

    return true;
}

bool app_melp_process_received_frame(app_melp_context_t *context,
                                     const uint8_t *frame_data,
                                     uint8_t frame_size)
{
    if (context == NULL || !context->initialized || frame_data == NULL) {
        return false;
    }

    if (frame_size != MELP_FRAME_SIZE_BYTES) {
        context->frame_errors++;
        return false;
    }

    app_melp_change_state(context, APP_MELP_STATE_RECEIVING);
    context->frames_received++;

    app_melp_change_state(context, APP_MELP_STATE_DECODING);
    
    if (app_melp_decode_frame(context, frame_data)) {
        context->frames_decoded++;
        app_melp_change_state(context, APP_MELP_STATE_PLAYING);
    } else {
        context->frame_errors++;
        app_melp_change_state(context, APP_MELP_STATE_ERROR);
        return false;
    }

    return true;
}

app_melp_state_t app_melp_get_state(const app_melp_context_t *context)
{
    if (context == NULL || !context->initialized) {
        return APP_MELP_STATE_ERROR;
    }

    return context->current_state;
}

bool app_melp_get_statistics(const app_melp_context_t *context,
                             uint32_t *frames_encoded,
                             uint32_t *frames_decoded,
                             uint32_t *frame_errors)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    if (frames_encoded != NULL) {
        *frames_encoded = context->frames_encoded;
    }
    
    if (frames_decoded != NULL) {
        *frames_decoded = context->frames_decoded;
    }
    
    if (frame_errors != NULL) {
        *frame_errors = context->frame_errors;
    }

    return true;
}

bool app_melp_reset_statistics(app_melp_context_t *context)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    context->frames_encoded = 0;
    context->frames_decoded = 0;
    context->frames_transmitted = 0;
    context->frames_received = 0;
    context->frame_errors = 0;

    return true;
}

bool app_melp_start_recording(app_melp_context_t *context)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    if (context->current_state == APP_MELP_STATE_IDLE) {
        app_melp_change_state(context, APP_MELP_STATE_RECORDING);
        context->input_samples_count = 0;
        return true;
    }

    return false;
}

bool app_melp_stop_recording(app_melp_context_t *context)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    if (context->current_state == APP_MELP_STATE_RECORDING) {
        app_melp_change_state(context, APP_MELP_STATE_IDLE);
        return true;
    }

    return false;
}

bool app_melp_start_playback(app_melp_context_t *context)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    if (context->current_state == APP_MELP_STATE_IDLE) {
        app_melp_change_state(context, APP_MELP_STATE_PLAYING);
        context->output_samples_count = 0;
        return true;
    }

    return false;
}

bool app_melp_stop_playback(app_melp_context_t *context)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    if (context->current_state == APP_MELP_STATE_PLAYING) {
        app_melp_change_state(context, APP_MELP_STATE_IDLE);
        return true;
    }

    return false;
}

/* ========================================================================== */
/*                           PRIVATE FUNCTION IMPLEMENTATIONS                */
/* ========================================================================== */

static void app_melp_change_state(app_melp_context_t *context, app_melp_state_t new_state)
{
    if (context == NULL) {
        return;
    }

    app_melp_state_t old_state = context->current_state;
    context->current_state = new_state;

    // Call state change callback
    if (callbacks_initialized && app_callbacks.on_state_changed != NULL) {
        app_callbacks.on_state_changed(old_state, new_state);
    }
}

static bool app_melp_encode_frame(app_melp_context_t *context)
{
    if (context == NULL || context->input_samples_count < MELP_FRAME_SIZE) {
        return false;
    }

    melp_encoded_frame_t encoded_frame;
    
    // Encode frame
    if (!melp_encoder_encode_frame(&context->encoder_state, 
                                   context->input_buffer, 
                                   &encoded_frame)) {
        return false;
    }

    // Pack frame into bit stream
    uint8_t frame_data[MELP_FRAME_SIZE_BYTES];
    uint8_t bits_packed = melp_encoder_pack_frame(&encoded_frame, frame_data);
    
    if (bits_packed != MELP_FRAME_SIZE_BITS) {
        return false;
    }

    // Store encoded frame
    if (context->encoded_frames_count < MELP_ENCODED_BUFFER_SIZE / MELP_FRAME_SIZE_BYTES) {
        uint32_t offset = context->encoded_frames_count * MELP_FRAME_SIZE_BYTES;
        memcpy(&context->encoded_frame_buffer[offset], frame_data, MELP_FRAME_SIZE_BYTES);
        context->encoded_frames_count++;
    }

    // Call frame encoded callback
    if (callbacks_initialized && app_callbacks.on_frame_encoded != NULL) {
        app_callbacks.on_frame_encoded(frame_data, MELP_FRAME_SIZE_BYTES);
    }

    return true;
}

static bool app_melp_decode_frame(app_melp_context_t *context, const uint8_t *frame_data)
{
    if (context == NULL || frame_data == NULL) {
        return false;
    }

    int16_t decoded_samples[MELP_FRAME_SIZE];
    
    // Decode frame
    if (!melp_decoder_decode_frame(&context->decoder_state, 
                                   frame_data, 
                                   decoded_samples)) {
        return false;
    }

    // Store decoded samples
    uint32_t samples_to_store = MELP_FRAME_SIZE;
    uint32_t buffer_space = AUDIO_OUTPUT_BUFFER_SIZE - context->output_samples_count;
    
    if (samples_to_store > buffer_space) {
        samples_to_store = buffer_space;
    }

    memcpy(&context->output_buffer[context->output_samples_count], 
           decoded_samples, 
           samples_to_store * sizeof(int16_t));
    
    context->output_samples_count += samples_to_store;

    // Call frame decoded callback
    if (callbacks_initialized && app_callbacks.on_frame_decoded != NULL) {
        app_callbacks.on_frame_decoded(decoded_samples, MELP_FRAME_SIZE);
    }

    return true;
}
