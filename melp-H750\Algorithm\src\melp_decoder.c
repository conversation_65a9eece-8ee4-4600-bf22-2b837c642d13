/* SPDX-License-Identifier: MIT */
/**
 * @file melp_decoder.c
 * @brief MELP (Mixed Excitation Linear Prediction) Decoder Implementation
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

#include "../inc/melp_decoder.h"
#include <string.h>
#include <math.h>
#include <stdlib.h>

/* ========================================================================== */
/*                           PRIVATE CONSTANTS                               */
/* ========================================================================== */

#define MELP_VERSION_STRING         "MELP Decoder v1.0.0"
#define MELP_PI                     3.14159265358979323846f
#define MELP_EPSILON                1e-10f

/* ========================================================================== */
/*                           PRIVATE VARIABLES                               */
/* ========================================================================== */

/** Mixed excitation filter bank coefficients */
// static melp_mixed_excitation_t mixed_excitation_filters;  // Commented out to remove warning
static bool filters_initialized = false;

/* ========================================================================== */
/*                           PRIVATE FUNCTION DECLARATIONS                   */
/* ========================================================================== */

// static void melp_init_mixed_excitation_filters(void);  // Commented out to remove warning
static void melp_generate_white_noise(float *output, uint32_t length);
static void melp_generate_pulse_train(float pitch, float *output, uint32_t length);
// static void melp_apply_mixed_excitation_filter(const float *input,   // Commented out to remove warning
//                                                const float *voicing,
//                                                float *output, uint32_t length);

/* ========================================================================== */
/*                           PUBLIC FUNCTION IMPLEMENTATIONS                 */
/* ========================================================================== */

bool melp_decoder_init(melp_decoder_state_t *state)
{
    if (state == NULL) {
        return false;
    }

    // Clear all memory
    memset(state, 0, sizeof(melp_decoder_state_t));

    // Initialize mixed excitation filters if not already done
    if (!filters_initialized) {
        // melp_init_mixed_excitation_filters();  // Commented out to remove warning
        filters_initialized = true;
    }

    // Initialize with default values
    for (int i = 0; i < MELP_LPC_ORDER; i++) {
        state->prev_lsf[i] = (float)(i + 1) * MELP_PI / (MELP_LPC_ORDER + 1);
    }
    state->prev_gain = 1000.0f;
    state->prev_pitch = 50.0f;

    state->initialized = true;
    return true;
}

bool melp_decoder_deinit(melp_decoder_state_t *state)
{
    if (state == NULL) {
        return false;
    }

    memset(state, 0, sizeof(melp_decoder_state_t));
    return true;
}

bool melp_decoder_decode_frame(melp_decoder_state_t *state,
                               const uint8_t *bit_stream,
                               int16_t *output_samples)
{
    if (state == NULL || bit_stream == NULL || output_samples == NULL) {
        return false;
    }

    if (!state->initialized) {
        return false;
    }

    melp_encoded_frame_t encoded_frame;
    melp_params_t params;
    float excitation[MELP_FRAME_SIZE];
    float synthesized[MELP_FRAME_SIZE];
    float enhanced[MELP_FRAME_SIZE];

    // Unpack bit stream
    if (melp_decoder_unpack_frame(bit_stream, &encoded_frame) == 0) {
        return false;
    }

    // Dequantize parameters
    if (!melp_dequantize_parameters(&encoded_frame, &params)) {
        return false;
    }

    // Convert LSF to LPC
    if (!melp_lsf_to_lpc(params.lsf_coeffs, params.lpc_coeffs)) {
        return false;
    }

    // Generate mixed excitation
    if (!melp_generate_excitation(params.pitch, params.voicing,
                                  params.aperiodic_flag, excitation,
                                  MELP_FRAME_SIZE)) {
        return false;
    }

    // LPC synthesis
    if (!melp_lpc_synthesis(excitation, params.lpc_coeffs,
                            state->synth_mem, synthesized,
                            MELP_FRAME_SIZE)) {
        return false;
    }

    // Adaptive spectral enhancement
    if (!melp_adaptive_spectral_enhancement(synthesized, params.lpc_coeffs,
                                            enhanced, MELP_FRAME_SIZE)) {
        return false;
    }

    // Post-filter
    if (!melp_post_filter(enhanced, params.lpc_coeffs,
                          state->postfilt_mem, synthesized,
                          MELP_FRAME_SIZE)) {
        return false;
    }

    // Convert to 16-bit output
    for (uint32_t i = 0; i < MELP_FRAME_SIZE; i++) {
        float sample = synthesized[i] * params.gain;
        
        // Clamp to 16-bit range
        if (sample > 32767.0f) sample = 32767.0f;
        if (sample < -32768.0f) sample = -32768.0f;
        
        output_samples[i] = (int16_t)sample;
    }

    // Update state
    memcpy(state->prev_lsf, params.lsf_coeffs, sizeof(state->prev_lsf));
    state->prev_gain = params.gain;
    state->prev_pitch = params.pitch;
    state->frame_count++;

    return true;
}

uint8_t melp_decoder_unpack_frame(const uint8_t *bit_stream,
                                  melp_encoded_frame_t *encoded_frame)
{
    if (bit_stream == NULL || encoded_frame == NULL) {
        return 0;
    }

    // Unpack 14-bit frame from 2 bytes
    uint16_t packed_bits = (uint16_t)bit_stream[0] | ((uint16_t)bit_stream[1] << 8);

    // Extract fields
    encoded_frame->lsf_indices[0] = (uint8_t)(packed_bits & 0x3F);
    encoded_frame->pitch_index = (uint8_t)((packed_bits >> 6) & 0x0F);
    encoded_frame->gain_index = (uint8_t)((packed_bits >> 10) & 0x07);
    encoded_frame->sync_bit = (uint8_t)((packed_bits >> 13) & 0x01);

    return 14; // 14 bits unpacked
}

bool melp_decoder_frame_erasure(melp_decoder_state_t *state,
                                int16_t *output_samples)
{
    if (state == NULL || output_samples == NULL) {
        return false;
    }

    // Simple frame erasure concealment - repeat previous frame with attenuation
    float attenuation = 0.8f;
    
    for (uint32_t i = 0; i < MELP_FRAME_SIZE; i++) {
        float sample = state->prev_gain * attenuation * 
                       sinf(2.0f * MELP_PI * state->prev_pitch * i / AUDIO_SAMPLE_RATE_HZ);
        
        if (sample > 32767.0f) sample = 32767.0f;
        if (sample < -32768.0f) sample = -32768.0f;
        
        output_samples[i] = (int16_t)sample;
    }

    return true;
}

const char* melp_decoder_get_version(void)
{
    return MELP_VERSION_STRING;
}

/* ========================================================================== */
/*                           INTERNAL FUNCTION IMPLEMENTATIONS               */
/* ========================================================================== */

bool melp_dequantize_parameters(const melp_encoded_frame_t *encoded_frame,
                                melp_params_t *params)
{
    if (encoded_frame == NULL || params == NULL) {
        return false;
    }

    // Dequantize LSF (simplified)
    for (int i = 0; i < MELP_LPC_ORDER; i++) {
        params->lsf_coeffs[i] = (float)(i + 1) * MELP_PI / (MELP_LPC_ORDER + 1);
    }

    // Dequantize pitch
    params->pitch = MELP_PITCH_MIN + 
                   (float)encoded_frame->pitch_index * 
                   (MELP_PITCH_MAX - MELP_PITCH_MIN) / 15.0f;

    // Dequantize gain
    params->gain = (float)encoded_frame->gain_index * 32768.0f / 7.0f;

    // Set voicing (simplified)
    float voicing_strength = (params->gain > 5000.0f) ? 0.8f : 0.2f;
    for (int i = 0; i < MELP_NUM_BANDS; i++) {
        params->voicing[i] = voicing_strength;
    }

    // Set aperiodic flag
    params->aperiodic_flag = (voicing_strength < 0.5f);

    return true;
}

bool melp_lsf_to_lpc(const float *lsf_coeffs, float *lpc_coeffs)
{
    // Simplified LSF to LPC conversion
    if (lsf_coeffs == NULL || lpc_coeffs == NULL) {
        return false;
    }

    // Placeholder implementation - actual conversion would use
    // Chebyshev polynomial evaluation
    lpc_coeffs[0] = 1.0f;
    for (int i = 1; i <= MELP_LPC_ORDER; i++) {
        lpc_coeffs[i] = 0.1f * sinf(lsf_coeffs[i-1]);
    }

    return true;
}

bool melp_generate_excitation(float pitch, const float *voicing,
                              bool aperiodic_flag, float *excitation,
                              uint32_t length)
{
    if (voicing == NULL || excitation == NULL) {
        return false;
    }

    float pulse_train[MELP_FRAME_SIZE];
    float noise[MELP_FRAME_SIZE];

    // Generate pulse train
    melp_generate_pulse_train(pitch, pulse_train, length);

    // Generate white noise
    melp_generate_white_noise(noise, length);

    // Mix excitation based on voicing
    for (uint32_t i = 0; i < length; i++) {
        float avg_voicing = 0.0f;
        for (int j = 0; j < MELP_NUM_BANDS; j++) {
            avg_voicing += voicing[j];
        }
        avg_voicing /= MELP_NUM_BANDS;

        if (aperiodic_flag) {
            excitation[i] = noise[i];
        } else {
            excitation[i] = avg_voicing * pulse_train[i] + 
                           (1.0f - avg_voicing) * noise[i];
        }
    }

    return true;
}

bool melp_lpc_synthesis(const float *excitation, const float *lpc_coeffs,
                        float *synth_mem, float *output, uint32_t length)
{
    if (excitation == NULL || lpc_coeffs == NULL || synth_mem == NULL || output == NULL) {
        return false;
    }

    // IIR synthesis filter: H(z) = 1 / A(z)
    for (uint32_t i = 0; i < length; i++) {
        float sum = excitation[i];
        
        // Feedback from previous outputs
        for (int j = 1; j <= MELP_LPC_ORDER; j++) {
            if (i >= j) {
                sum -= lpc_coeffs[j] * output[i - j];
            } else {
                sum -= lpc_coeffs[j] * synth_mem[MELP_LPC_ORDER - j + i];
            }
        }
        
        output[i] = sum;
    }

    // Update synthesis memory
    for (int i = 0; i < MELP_LPC_ORDER; i++) {
        if (length > MELP_LPC_ORDER) {
            synth_mem[i] = output[length - MELP_LPC_ORDER + i];
        } else {
            synth_mem[i] = output[i];
        }
    }

    return true;
}

bool melp_adaptive_spectral_enhancement(const float *input,
                                        const float *lpc_coeffs,
                                        float *output, uint32_t length)
{
    // Simplified ASE - actual implementation would use pole enhancement
    if (input == NULL || lpc_coeffs == NULL || output == NULL) {
        return false;
    }

    for (uint32_t i = 0; i < length; i++) {
        output[i] = input[i] * (1.0f + MELP_ASE_FACTOR);
    }

    return true;
}

bool melp_post_filter(const float *input, const float *lpc_coeffs,
                      float *postfilt_mem, float *output, uint32_t length)
{
    // Simplified post-filter
    if (input == NULL || lpc_coeffs == NULL || postfilt_mem == NULL || output == NULL) {
        return false;
    }

    // Simple high-pass filter
    for (uint32_t i = 0; i < length; i++) {
        output[i] = input[i] - 0.95f * ((i > 0) ? input[i-1] : postfilt_mem[0]);
    }

    if (length > 0) {
        postfilt_mem[0] = input[length - 1];
    }

    return true;
}

bool melp_pulse_dispersion(const float *input, float *output, uint32_t length)
{
    // Simplified pulse dispersion
    if (input == NULL || output == NULL) {
        return false;
    }

    for (uint32_t i = 0; i < length; i++) {
        output[i] = input[i] * (1.0f - MELP_PULSE_DISP_FACTOR);
    }

    return true;
}

/* ========================================================================== */
/*                           PRIVATE FUNCTION IMPLEMENTATIONS                */
/* ========================================================================== */

/*  // Commented out to remove warning
static void melp_init_mixed_excitation_filters(void)
{
    // Initialize simplified mixed excitation filter bank
    // Actual implementation would have proper bandpass filters
    for (int band = 0; band < MELP_NUM_BANDS; band++) {
        for (int i = 0; i < 8; i++) {
            mixed_excitation_filters.coeffs[band][i] = 0.1f;
            mixed_excitation_filters.mem[band][i] = 0.0f;
        }
    }
}
*/

static void melp_generate_white_noise(float *output, uint32_t length)
{
    for (uint32_t i = 0; i < length; i++) {
        // Simple pseudo-random noise generator
        static uint32_t seed = 12345;
        seed = seed * ********** + 12345;
        output[i] = ((float)((int32_t)seed) / **********.0f) * 1000.0f;
    }
}

static void melp_generate_pulse_train(float pitch, float *output, uint32_t length)
{
    float period = AUDIO_SAMPLE_RATE_HZ / pitch;
    
    for (uint32_t i = 0; i < length; i++) {
        float phase = fmodf((float)i, period) / period;
        output[i] = (phase < 0.1f) ? 1000.0f : 0.0f;
    }
}

/*  // Commented out to remove warning
static void melp_apply_mixed_excitation_filter(const float *input,
                                               const float *voicing,
                                               float *output, uint32_t length)
{
    // Simplified mixed excitation filtering
    for (uint32_t i = 0; i < length; i++) {
        output[i] = input[i];
    }
}
*/
