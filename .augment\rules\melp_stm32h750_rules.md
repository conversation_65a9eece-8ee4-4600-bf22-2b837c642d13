---
type: "always_apply"
---

# Cursor 开发规则（MELP-STM32H750VBT6 项目）

> 本规则文件供 AI 及协同开发者在 Cursor 中参考，确保后续自动生成代码或文档时不偏离项目目标。
文档路径 ./prj_docs/melp-stm32h750vbt6/
工作日志路径 ./prj_docs/melp-stm32h750vbt6/开发工作日志.md


## 1. 项目边界
1. 目标：在 STM32H750VBT6 上实现 600 bps MELP 语音压缩，按键录音 → 串口实时输出；串口回传 → 解码播放。
2. 数据流：**绝不**将压缩数据写入外部 Flash/QSPI；仅使用 UART(921600 bps)。
3. 采样率固定 8 kHz，ADC 12 bit，DAC 12 bit，单声道。

## 2. 目录结构（固定）
- `app/`、`hal/`、`alg/`、`middleware/`、`tests/`、`scripts/` 等；新增模块必须征得同意后建子目录。

## 3. 代码规范
1. 语言：C99，必要时可用 C++17（需 justify）。
2. 命名：`snake_case` for C 函数/变量；`PascalCase` for struct/enum 类型。
3. 代码文件头必须含 SPDX-License-Identifier: MIT。
4. 严禁硬编码魔数，统一放入 `inc/project_config.h`。
5. 所有 DSP 运算优先调用 CMSIS-DSP/ST DSP 库，不可重复造轮子。

## 4. 驱动层约束
- 仅使用 STM32Cube HAL；不得直接操作寄存器除非 HAL 不支持且留注释。
- UART 必须 DMA + 双缓冲 + 可选 RTS/CTS。

## 5. 算法层约束
1. 与 `docs/*算法详细设计文档` 保持一致：LSP Split-VQ、增益/基音/浊度量化表不可随意修改。
2. 如果需优化参数，先在 `tests/` 添加对比用例并通过。

## 6. 文档同步
- **先文档、后代码**：重要架构/接口变更，先更新设计文档并通过 Review。
- 做了工作日志记录，文档名字为 《开发工作日志》，工作日志中每次要记录时间，时间必须获取真实事件，精确到分钟，工作日志只能增加，最新的日志在最前面

## 7. 不得做的事
- 不得重新引入 Flash 存储路径。
- 不得降低 UART 波特率 < 921600 bps。
- 不得擅改 ADC 采样率、Vref、电阻电容值。

## 8. 提交 & PR
1. commit msg：`<module>: <subject>`，如 `alg: add splitvq codebook`。
2. PR 模板需勾选"遵守本规则"。

## 9. 测试自动化
- 所有新算法代码需附 Unity 单元测试；CI 失败禁止合并。

---
如需变更本规则，请先在 PR 中说明理由，经项目负责人确认后方可修改。 