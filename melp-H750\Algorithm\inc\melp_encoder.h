/* SPDX-License-Identifier: MIT */
/**
 * @file melp_encoder.h
 * @brief MELP (Mixed Excitation Linear Prediction) Encoder Interface
 * <AUTHOR> Assistant
 * @date 2025-01-19
 * 
 * Based on MIL-STD-3005 and STANAG-4591 specifications
 * Implements 600bps MELP vocoder encoder
 */

#ifndef MELP_ENCODER_H
#define MELP_ENCODER_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "project_config.h"

/* ========================================================================== */
/*                           MELP ENCODER CONSTANTS                          */
/* ========================================================================== */

/** MELP frame length in samples (22.5ms at 8kHz) */
#define MELP_FRAME_SIZE             AUDIO_FRAME_LENGTH_SAMPLES

/** MELP LPC analysis order */
#define MELP_LPC_ORDER_ALG          MELP_LPC_ORDER

/** Number of mixed excitation subbands */
#define MELP_NUM_BANDS              MELP_NUM_SUBBANDS

/** Pitch search range */
#define MELP_PITCH_MIN              20
#define MELP_PITCH_MAX              160

/** Voicing threshold */
#define MELP_VOICING_THRESH         0.6f

/** Pre-emphasis coefficient */
#define MELP_PREEMPH_COEFF          0.8f

/* ========================================================================== */
/*                           TYPE DEFINITIONS                                */
/* ========================================================================== */

/**
 * @brief MELP encoder parameters structure
 */
typedef struct {
    float lpc_coeffs[MELP_LPC_ORDER_ALG + 1];      /**< LPC coefficients */
    float lsf_coeffs[MELP_LPC_ORDER_ALG];          /**< Line Spectral Frequencies */
    float pitch;                                /**< Fundamental frequency */
    float gain;                                 /**< Frame gain */
    float voicing[MELP_NUM_BANDS];             /**< Voicing strengths per band */
    bool aperiodic_flag;                       /**< Aperiodic pulse flag */
    float fourier_mags[MELP_NUM_BANDS];        /**< Fourier magnitude coefficients */
} melp_params_t;

/**
 * @brief MELP encoder state structure
 */
typedef struct {
    float input_buffer[MELP_FRAME_SIZE * 2];   /**< Input speech buffer */
    float preemph_mem;                         /**< Pre-emphasis memory */
    float pitch_mem;                           /**< Previous pitch value */
    float gain_mem;                            /**< Previous gain value */
    float lpc_mem[MELP_LPC_ORDER_ALG];             /**< LPC analysis memory */
    uint32_t frame_count;                      /**< Frame counter */
    bool initialized;                          /**< Initialization flag */
} melp_encoder_state_t;

/**
 * @brief MELP encoded frame structure (14 bits total for 600bps)
 */
typedef struct {
    uint8_t lsf_indices[6];                    /**< Quantized LSF indices (6 bits) */
    uint8_t pitch_index;                       /**< Quantized pitch index (7 bits) */
    uint8_t gain_index;                        /**< Quantized gain index (5 bits) */
    uint8_t voicing_index;                     /**< Quantized voicing index (4 bits) */
    uint8_t sync_bit;                          /**< Synchronization bit (1 bit) */
    // Total: 6+7+5+4+1 = 23 bits (need to optimize to 14 bits)
} melp_encoded_frame_t;

/* ========================================================================== */
/*                           FUNCTION DECLARATIONS                           */
/* ========================================================================== */

/**
 * @brief Initialize MELP encoder
 * @param state Pointer to encoder state structure
 * @return true if successful, false otherwise
 */
bool melp_encoder_init(melp_encoder_state_t *state);

/**
 * @brief Deinitialize MELP encoder
 * @param state Pointer to encoder state structure
 * @return true if successful, false otherwise
 */
bool melp_encoder_deinit(melp_encoder_state_t *state);

/**
 * @brief Encode one frame of speech
 * @param state Pointer to encoder state structure
 * @param input_samples Input speech samples (180 samples)
 * @param encoded_frame Output encoded frame
 * @return true if successful, false otherwise
 */
bool melp_encoder_encode_frame(melp_encoder_state_t *state,
                               const int16_t *input_samples,
                               melp_encoded_frame_t *encoded_frame);

/**
 * @brief Pack encoded frame into bit stream
 * @param encoded_frame Input encoded frame
 * @param bit_stream Output bit stream (2 bytes for 14 bits)
 * @return Number of bits packed
 */
uint8_t melp_encoder_pack_frame(const melp_encoded_frame_t *encoded_frame,
                                uint8_t *bit_stream);

/**
 * @brief Get encoder version information
 * @return Version string
 */
const char* melp_encoder_get_version(void);

/* ========================================================================== */
/*                           INTERNAL FUNCTION DECLARATIONS                  */
/* ========================================================================== */

/**
 * @brief Perform LPC analysis
 * @param input Input speech samples
 * @param lpc_coeffs Output LPC coefficients
 * @param gain Output frame gain
 * @return true if successful, false otherwise
 */
bool melp_lpc_analysis(const float *input, float *lpc_coeffs, float *gain);

/**
 * @brief Convert LPC coefficients to Line Spectral Frequencies
 * @param lpc_coeffs Input LPC coefficients
 * @param lsf_coeffs Output LSF coefficients
 * @return true if successful, false otherwise
 */
bool melp_lpc_to_lsf(const float *lpc_coeffs, float *lsf_coeffs);

/**
 * @brief Perform pitch estimation
 * @param input Input speech samples
 * @param pitch Output pitch value
 * @return true if successful, false otherwise
 */
bool melp_pitch_estimation(const float *input, float *pitch);

/**
 * @brief Analyze voicing characteristics
 * @param input Input speech samples
 * @param pitch Pitch value
 * @param voicing Output voicing strengths per band
 * @return true if successful, false otherwise
 */
bool melp_voicing_analysis(const float *input, float pitch, float *voicing);

/**
 * @brief Quantize MELP parameters
 * @param params Input MELP parameters
 * @param encoded_frame Output quantized parameters
 * @return true if successful, false otherwise
 */
bool melp_quantize_parameters(const melp_params_t *params,
                              melp_encoded_frame_t *encoded_frame);

#ifdef __cplusplus
}
#endif

#endif /* MELP_ENCODER_H */
