/* SPDX-License-Identifier: MIT */
/**
 * @file app_state_machine.c
 * @brief Application state machine implementation
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

#include "app_state_machine.h"
#include <string.h>

/* ========================================================================== */
/*                           PRIVATE CONSTANTS                               */
/* ========================================================================== */

#define MAX_RECOVERY_ATTEMPTS       3
#define DEFAULT_TIMEOUT_MS          5000
#define ERROR_RECOVERY_TIMEOUT_MS   2000

/* ========================================================================== */
/*                           PRIVATE VARIABLES                               */
/* ========================================================================== */

static app_sm_callbacks_t sm_callbacks;
static bool callbacks_initialized = false;

/* ========================================================================== */
/*                           PRIVATE FUNCTION DECLARATIONS                   */
/* ========================================================================== */

static bool app_sm_transition_to(app_sm_context_t *context, app_sm_state_t new_state, app_sm_event_t event);
static bool app_sm_is_valid_transition(app_sm_state_t from, app_sm_state_t to, app_sm_event_t event);
static void app_sm_handle_state_entry(app_sm_context_t *context, app_sm_state_t state);
static void app_sm_handle_state_exit(app_sm_context_t *context, app_sm_state_t state);
static bool app_sm_handle_timeout(app_sm_context_t *context);

/* ========================================================================== */
/*                           STATE NAME TABLES                               */
/* ========================================================================== */

static const char* state_names[APP_SM_STATE_COUNT] = {
    "INIT", "IDLE", "RECORDING", "ENCODING", "TRANSMITTING",
    "RECEIVING", "DECODING", "PLAYING", "ERROR", "RECOVERY", "SHUTDOWN"
};

static const char* event_names[APP_SM_EVENT_COUNT] = {
    "NONE", "INIT_COMPLETE", "BUTTON_PRESSED", "BUTTON_RELEASED",
    "AUDIO_FRAME_READY", "FRAME_ENCODED", "FRAME_TRANSMITTED",
    "FRAME_RECEIVED", "FRAME_DECODED", "PLAYBACK_COMPLETE",
    "ERROR_OCCURRED", "RECOVERY_COMPLETE", "TIMEOUT", "SHUTDOWN_REQUEST"
};

static const char* error_names[APP_SM_ERROR_COUNT] = {
    "NONE", "HARDWARE_FAILURE", "MEMORY_ALLOCATION", "CODEC_FAILURE",
    "COMMUNICATION_FAILURE", "TIMEOUT", "INVALID_STATE", "INVALID_EVENT",
    "BUFFER_OVERFLOW", "BUFFER_UNDERFLOW"
};

/* ========================================================================== */
/*                           PUBLIC FUNCTION IMPLEMENTATIONS                 */
/* ========================================================================== */

bool app_sm_init(app_sm_context_t *context, const app_sm_callbacks_t *callbacks)
{
    if (context == NULL) {
        return false;
    }

    // Clear context
    memset(context, 0, sizeof(app_sm_context_t));

    // Store callbacks
    if (callbacks != NULL) {
        memcpy(&sm_callbacks, callbacks, sizeof(app_sm_callbacks_t));
        callbacks_initialized = true;
    }

    // Initialize state machine
    context->current_state = APP_SM_STATE_INIT;
    context->previous_state = APP_SM_STATE_INIT;
    context->last_event = APP_SM_EVENT_NONE;
    context->last_error = APP_SM_ERROR_NONE;
    context->timeout_value = DEFAULT_TIMEOUT_MS;
    context->error_recovery_enabled = true;
    context->initialized = true;

    // Handle initial state entry
    app_sm_handle_state_entry(context, APP_SM_STATE_INIT);

    return true;
}

bool app_sm_deinit(app_sm_context_t *context)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    // Handle final state exit
    app_sm_handle_state_exit(context, context->current_state);

    // Clear context
    memset(context, 0, sizeof(app_sm_context_t));
    callbacks_initialized = false;

    return true;
}

bool app_sm_process_event(app_sm_context_t *context, app_sm_event_t event)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    context->last_event = event;
    context->event_count[event]++;

    app_sm_state_t current_state = context->current_state;
    app_sm_state_t new_state = current_state;

    // State transition logic
    switch (current_state) {
        case APP_SM_STATE_INIT:
            if (event == APP_SM_EVENT_INIT_COMPLETE) {
                new_state = APP_SM_STATE_IDLE;
            }
            break;

        case APP_SM_STATE_IDLE:
            if (event == APP_SM_EVENT_BUTTON_PRESSED) {
                new_state = APP_SM_STATE_RECORDING;
            } else if (event == APP_SM_EVENT_FRAME_RECEIVED) {
                new_state = APP_SM_STATE_DECODING;
            }
            break;

        case APP_SM_STATE_RECORDING:
            if (event == APP_SM_EVENT_BUTTON_RELEASED) {
                new_state = APP_SM_STATE_IDLE;
            } else if (event == APP_SM_EVENT_AUDIO_FRAME_READY) {
                new_state = APP_SM_STATE_ENCODING;
            }
            break;

        case APP_SM_STATE_ENCODING:
            if (event == APP_SM_EVENT_FRAME_ENCODED) {
                new_state = APP_SM_STATE_TRANSMITTING;
            }
            break;

        case APP_SM_STATE_TRANSMITTING:
            if (event == APP_SM_EVENT_FRAME_TRANSMITTED) {
                new_state = APP_SM_STATE_RECORDING;
            }
            break;

        case APP_SM_STATE_RECEIVING:
            if (event == APP_SM_EVENT_FRAME_RECEIVED) {
                new_state = APP_SM_STATE_DECODING;
            }
            break;

        case APP_SM_STATE_DECODING:
            if (event == APP_SM_EVENT_FRAME_DECODED) {
                new_state = APP_SM_STATE_PLAYING;
            }
            break;

        case APP_SM_STATE_PLAYING:
            if (event == APP_SM_EVENT_PLAYBACK_COMPLETE) {
                new_state = APP_SM_STATE_IDLE;
            }
            break;

        case APP_SM_STATE_ERROR:
            if (event == APP_SM_EVENT_RECOVERY_COMPLETE) {
                new_state = context->previous_state;
            } else if (event == APP_SM_EVENT_TIMEOUT) {
                new_state = APP_SM_STATE_RECOVERY;
            }
            break;

        case APP_SM_STATE_RECOVERY:
            if (event == APP_SM_EVENT_RECOVERY_COMPLETE) {
                new_state = APP_SM_STATE_IDLE;
            }
            break;

        default:
            break;
    }

    // Handle error events in any state
    if (event == APP_SM_EVENT_ERROR_OCCURRED) {
        new_state = APP_SM_STATE_ERROR;
    }

    // Handle shutdown events in any state
    if (event == APP_SM_EVENT_SHUTDOWN_REQUEST) {
        new_state = APP_SM_STATE_SHUTDOWN;
    }

    // Perform state transition if needed
    if (new_state != current_state) {
        return app_sm_transition_to(context, new_state, event);
    }

    return true;
}

bool app_sm_report_error(app_sm_context_t *context, app_sm_error_t error)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    context->last_error = error;
    context->error_count++;
    context->error_count_by_type[error]++;

    // Call error callback
    if (callbacks_initialized && sm_callbacks.on_error != NULL) {
        sm_callbacks.on_error(error, context->current_state);
    }

    // Trigger error event
    return app_sm_process_event(context, APP_SM_EVENT_ERROR_OCCURRED);
}

bool app_sm_update(app_sm_context_t *context, uint32_t current_time)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    // Update state duration
    context->state_duration = current_time - context->state_entry_time;
    context->time_in_state[context->current_state] += 1;  // Increment time counter

    // Check for timeout
    if (context->timeout_enabled && 
        context->state_duration > context->timeout_value) {
        
        // Call timeout callback
        if (callbacks_initialized && sm_callbacks.on_timeout != NULL) {
            sm_callbacks.on_timeout(context->current_state, context->state_duration);
        }

        // Handle timeout
        return app_sm_handle_timeout(context);
    }

    return true;
}

app_sm_state_t app_sm_get_state(const app_sm_context_t *context)
{
    if (context == NULL || !context->initialized) {
        return APP_SM_STATE_ERROR;
    }

    return context->current_state;
}

const char* app_sm_get_state_name(app_sm_state_t state)
{
    if (state >= APP_SM_STATE_COUNT) {
        return "UNKNOWN";
    }
    return state_names[state];
}

const char* app_sm_get_event_name(app_sm_event_t event)
{
    if (event >= APP_SM_EVENT_COUNT) {
        return "UNKNOWN";
    }
    return event_names[event];
}

const char* app_sm_get_error_name(app_sm_error_t error)
{
    if (error >= APP_SM_ERROR_COUNT) {
        return "UNKNOWN";
    }
    return error_names[error];
}

bool app_sm_get_statistics(const app_sm_context_t *context,
                           uint32_t *total_transitions,
                           uint32_t *total_errors,
                           uint32_t *uptime_ms)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    if (total_transitions != NULL) {
        *total_transitions = context->state_transitions;
    }

    if (total_errors != NULL) {
        *total_errors = context->error_count;
    }

    if (uptime_ms != NULL) {
        uint32_t total_time = 0;
        for (int i = 0; i < APP_SM_STATE_COUNT; i++) {
            total_time += context->time_in_state[i];
        }
        *uptime_ms = total_time;
    }

    return true;
}

bool app_sm_reset_statistics(app_sm_context_t *context)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    context->error_count = 0;
    context->recovery_attempts = 0;
    context->state_transitions = 0;

    memset(context->time_in_state, 0, sizeof(context->time_in_state));
    memset(context->event_count, 0, sizeof(context->event_count));
    memset(context->error_count_by_type, 0, sizeof(context->error_count_by_type));

    return true;
}

bool app_sm_set_debug_mode(app_sm_context_t *context, bool enable)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    context->debug_mode = enable;
    return true;
}

bool app_sm_force_state(app_sm_context_t *context, app_sm_state_t new_state)
{
    if (context == NULL || !context->initialized) {
        return false;
    }

    if (new_state >= APP_SM_STATE_COUNT) {
        return false;
    }

    return app_sm_transition_to(context, new_state, APP_SM_EVENT_NONE);
}

/* ========================================================================== */
/*                           PRIVATE FUNCTION IMPLEMENTATIONS                */
/* ========================================================================== */

static bool app_sm_transition_to(app_sm_context_t *context, app_sm_state_t new_state, app_sm_event_t event)
{
    if (context == NULL || new_state >= APP_SM_STATE_COUNT) {
        return false;
    }

    app_sm_state_t old_state = context->current_state;

    // Validate transition
    if (!app_sm_is_valid_transition(old_state, new_state, event)) {
        app_sm_report_error(context, APP_SM_ERROR_INVALID_STATE);
        return false;
    }

    // Handle state exit
    app_sm_handle_state_exit(context, old_state);

    // Update state
    context->previous_state = old_state;
    context->current_state = new_state;
    context->state_transitions++;
    context->state_entry_time = 0;  // Should be set by caller with current time
    context->state_duration = 0;

    // Handle state entry
    app_sm_handle_state_entry(context, new_state);

    // Call transition callback
    if (callbacks_initialized && sm_callbacks.on_state_transition != NULL) {
        sm_callbacks.on_state_transition(old_state, new_state, event);
    }

    return true;
}

static bool app_sm_is_valid_transition(app_sm_state_t from, app_sm_state_t to, app_sm_event_t event)
{
    // Allow transitions to error and shutdown states from any state
    if (to == APP_SM_STATE_ERROR || to == APP_SM_STATE_SHUTDOWN) {
        return true;
    }

    // Allow recovery transitions
    if (from == APP_SM_STATE_ERROR && to == APP_SM_STATE_RECOVERY) {
        return true;
    }

    // Define valid state transitions
    switch (from) {
        case APP_SM_STATE_INIT:
            return (to == APP_SM_STATE_IDLE);

        case APP_SM_STATE_IDLE:
            return (to == APP_SM_STATE_RECORDING || to == APP_SM_STATE_DECODING);

        case APP_SM_STATE_RECORDING:
            return (to == APP_SM_STATE_IDLE || to == APP_SM_STATE_ENCODING);

        case APP_SM_STATE_ENCODING:
            return (to == APP_SM_STATE_TRANSMITTING || to == APP_SM_STATE_RECORDING);

        case APP_SM_STATE_TRANSMITTING:
            return (to == APP_SM_STATE_RECORDING || to == APP_SM_STATE_IDLE);

        case APP_SM_STATE_RECEIVING:
            return (to == APP_SM_STATE_DECODING || to == APP_SM_STATE_IDLE);

        case APP_SM_STATE_DECODING:
            return (to == APP_SM_STATE_PLAYING || to == APP_SM_STATE_IDLE);

        case APP_SM_STATE_PLAYING:
            return (to == APP_SM_STATE_IDLE || to == APP_SM_STATE_DECODING);

        case APP_SM_STATE_RECOVERY:
            return (to == APP_SM_STATE_IDLE);

        default:
            return false;
    }
}

static void app_sm_handle_state_entry(app_sm_context_t *context, app_sm_state_t state)
{
    if (context == NULL) {
        return;
    }

    // Set state-specific timeouts
    switch (state) {
        case APP_SM_STATE_ENCODING:
        case APP_SM_STATE_DECODING:
            context->timeout_value = 1000;  // 1 second for codec operations
            context->timeout_enabled = true;
            break;

        case APP_SM_STATE_TRANSMITTING:
            context->timeout_value = 500;   // 500ms for transmission
            context->timeout_enabled = true;
            break;

        case APP_SM_STATE_ERROR:
            context->timeout_value = ERROR_RECOVERY_TIMEOUT_MS;
            context->timeout_enabled = true;
            break;

        case APP_SM_STATE_RECOVERY:
            context->timeout_value = 3000;  // 3 seconds for recovery
            context->timeout_enabled = true;
            break;

        default:
            context->timeout_enabled = false;
            break;
    }

    // Call state entry callback
    if (callbacks_initialized && sm_callbacks.on_state_entry != NULL) {
        sm_callbacks.on_state_entry(state);
    }
}

static void app_sm_handle_state_exit(app_sm_context_t *context, app_sm_state_t state)
{
    if (context == NULL) {
        return;
    }

    // Call state exit callback
    if (callbacks_initialized && sm_callbacks.on_state_exit != NULL) {
        sm_callbacks.on_state_exit(state);
    }
}

static bool app_sm_handle_timeout(app_sm_context_t *context)
{
    if (context == NULL) {
        return false;
    }

    // Handle timeout based on current state
    switch (context->current_state) {
        case APP_SM_STATE_ENCODING:
        case APP_SM_STATE_DECODING:
        case APP_SM_STATE_TRANSMITTING:
            // Codec or transmission timeout - report error
            app_sm_report_error(context, APP_SM_ERROR_TIMEOUT);
            break;

        case APP_SM_STATE_ERROR:
            // Error state timeout - try recovery
            if (context->error_recovery_enabled &&
                context->recovery_attempts < MAX_RECOVERY_ATTEMPTS) {
                context->recovery_attempts++;
                app_sm_process_event(context, APP_SM_EVENT_RECOVERY_COMPLETE);
            } else {
                // Max recovery attempts reached - go to shutdown
                app_sm_process_event(context, APP_SM_EVENT_SHUTDOWN_REQUEST);
            }
            break;

        case APP_SM_STATE_RECOVERY:
            // Recovery timeout - back to idle
            app_sm_process_event(context, APP_SM_EVENT_RECOVERY_COMPLETE);
            break;

        default:
            // Other states - just disable timeout
            context->timeout_enabled = false;
            break;
    }

    return true;
}
