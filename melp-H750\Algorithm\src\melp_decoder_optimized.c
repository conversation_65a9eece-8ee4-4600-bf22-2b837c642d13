/* SPDX-License-Identifier: MIT */
/**
 * @file melp_decoder_optimized.c
 * @brief Optimized MELP decoder using CMSIS-DSP
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

#include "../inc/melp_decoder.h"
#include "../inc/melp_dsp.h"
#include <string.h>
#include <math.h>
#include <stdlib.h>

/* ========================================================================== */
/*                           PRIVATE VARIABLES                               */
/* ========================================================================== */

static melp_dsp_context_t dsp_context;
static bool optimized_decoder_initialized = false;

/* ========================================================================== */
/*                           PRIVATE FUNCTION DECLARATIONS                   */
/* ========================================================================== */

static bool melp_generate_excitation_optimized(float32_t pitch, const float32_t *voicing,
                                               bool aperiodic_flag, float32_t *excitation,
                                               uint32_t length);
static bool melp_lpc_synthesis_optimized(const float32_t *excitation, const float32_t *lpc_coeffs,
                                         float32_t *synth_mem, float32_t *output, uint32_t length);
static void melp_generate_white_noise_optimized(float32_t *output, uint32_t length);
static void melp_generate_pulse_train_optimized(float32_t pitch, float32_t *output, uint32_t length);
static bool melp_adaptive_spectral_enhancement_optimized(const float32_t *input,
                                                         const float32_t *lpc_coeffs,
                                                         float32_t *output, uint32_t length);

/* ========================================================================== */
/*                           PUBLIC FUNCTION IMPLEMENTATIONS                 */
/* ========================================================================== */

bool melp_decoder_init_optimized(melp_decoder_state_t *state)
{
    if (state == NULL) {
        return false;
    }

    // Initialize base decoder
    if (!melp_decoder_init(state)) {
        return false;
    }

    // Initialize DSP context
    if (!optimized_decoder_initialized) {
        if (!melp_dsp_init(&dsp_context)) {
            return false;
        }
        optimized_decoder_initialized = true;
    }

    return true;
}

bool melp_decoder_deinit_optimized(melp_decoder_state_t *state)
{
    if (state == NULL) {
        return false;
    }

    // Deinitialize DSP context
    if (optimized_decoder_initialized) {
        melp_dsp_deinit(&dsp_context);
        optimized_decoder_initialized = false;
    }

    // Deinitialize base decoder
    return melp_decoder_deinit(state);
}

bool melp_decoder_decode_frame_optimized(melp_decoder_state_t *state,
                                         const uint8_t *bit_stream,
                                         int16_t *output_samples)
{
    if (state == NULL || bit_stream == NULL || output_samples == NULL) {
        return false;
    }

    if (!state->initialized) {
        return false;
    }

    melp_encoded_frame_t encoded_frame;
    melp_params_t params;
    float32_t excitation[MELP_FRAME_SIZE];
    float32_t synthesized[MELP_FRAME_SIZE];
    float32_t enhanced[MELP_FRAME_SIZE];

    // Unpack bit stream (using existing function)
    if (melp_decoder_unpack_frame(bit_stream, &encoded_frame) == 0) {
        return false;
    }

    // Dequantize parameters (using existing function)
    if (!melp_dequantize_parameters(&encoded_frame, &params)) {
        return false;
    }

    // Convert LSF to LPC (using existing function)
    if (!melp_lsf_to_lpc(params.lsf_coeffs, params.lpc_coeffs)) {
        return false;
    }

    // Generate mixed excitation (optimized)
    if (!melp_generate_excitation_optimized(params.pitch, params.voicing,
                                            params.aperiodic_flag, excitation,
                                            MELP_FRAME_SIZE)) {
        return false;
    }

    // LPC synthesis (optimized)
    if (!melp_lpc_synthesis_optimized(excitation, params.lpc_coeffs,
                                     state->synth_mem, synthesized,
                                     MELP_FRAME_SIZE)) {
        return false;
    }

    // Adaptive spectral enhancement (optimized)
    if (!melp_adaptive_spectral_enhancement_optimized(synthesized, params.lpc_coeffs,
                                                      enhanced, MELP_FRAME_SIZE)) {
        return false;
    }

    // Post-filter (optimized)
    if (!melp_dsp_post_filter(&dsp_context, enhanced, params.lpc_coeffs,
                              synthesized, MELP_FRAME_SIZE)) {
        return false;
    }

    // Convert to 16-bit output with optimized scaling
    float32_t scale_factor = params.gain / 1000.0f;  // Normalize gain
    for (uint32_t i = 0; i < MELP_FRAME_SIZE; i++) {
        float32_t sample = synthesized[i] * scale_factor;
        
        // Clamp to 16-bit range
        if (sample > 32767.0f) sample = 32767.0f;
        if (sample < -32768.0f) sample = -32768.0f;
        
        output_samples[i] = (int16_t)sample;
    }

    // Update state
    memcpy(state->prev_lsf, params.lsf_coeffs, sizeof(state->prev_lsf));
    state->prev_gain = params.gain;
    state->prev_pitch = params.pitch;
    state->frame_count++;

    return true;
}

/* ========================================================================== */
/*                           PRIVATE FUNCTION IMPLEMENTATIONS                */
/* ========================================================================== */

static bool melp_generate_excitation_optimized(float32_t pitch, const float32_t *voicing,
                                               bool aperiodic_flag, float32_t *excitation,
                                               uint32_t length)
{
    if (voicing == NULL || excitation == NULL) {
        return false;
    }

    float32_t pulse_train[MELP_FRAME_SIZE];
    float32_t noise[MELP_FRAME_SIZE];

    // Generate pulse train (optimized)
    melp_generate_pulse_train_optimized(pitch, pulse_train, length);

    // Generate white noise (optimized)
    melp_generate_white_noise_optimized(noise, length);

    // Calculate average voicing
    float32_t avg_voicing = 0.0f;
    for (int j = 0; j < MELP_NUM_BANDS; j++) {
        avg_voicing += voicing[j];
    }
    avg_voicing /= MELP_NUM_BANDS;

    // Mix excitation based on voicing (optimized)
    if (aperiodic_flag) {
        // Pure noise excitation
        memcpy(excitation, noise, length * sizeof(float32_t));
    } else {
        // Mixed excitation using CMSIS-DSP
        float32_t voiced_scale = avg_voicing;
        float32_t unvoiced_scale = 1.0f - avg_voicing;
        
        // Scale vectors
        melp_dsp_vector_scale(pulse_train, voiced_scale, pulse_train, length);
        melp_dsp_vector_scale(noise, unvoiced_scale, noise, length);
        
        // Add vectors
        for (uint32_t i = 0; i < length; i++) {
            excitation[i] = pulse_train[i] + noise[i];
        }
    }

    return true;
}

static bool melp_lpc_synthesis_optimized(const float32_t *excitation, const float32_t *lpc_coeffs,
                                         float32_t *synth_mem, float32_t *output, uint32_t length)
{
    if (excitation == NULL || lpc_coeffs == NULL || synth_mem == NULL || output == NULL) {
        return false;
    }

    // Use optimized LPC synthesis
    return melp_dsp_lpc_synthesis(lpc_coeffs, excitation, output, length, 
                                  MELP_LPC_ORDER, synth_mem);
}

static bool melp_adaptive_spectral_enhancement_optimized(const float32_t *input,
                                                         const float32_t *lpc_coeffs,
                                                         float32_t *output, uint32_t length)
{
    if (input == NULL || lpc_coeffs == NULL || output == NULL) {
        return false;
    }

    // Enhanced spectral enhancement using formant emphasis
    float32_t enhancement_factor = 1.0f + MELP_ASE_FACTOR;
    
    // Apply enhancement with optimized scaling
    melp_dsp_vector_scale(input, enhancement_factor, output, length);
    
    // Apply formant sharpening (simplified)
    for (uint32_t i = 1; i < length - 1; i++) {
        float32_t derivative = output[i+1] - output[i-1];
        output[i] += 0.1f * derivative;  // Enhance spectral peaks
    }

    return true;
}

static void melp_generate_white_noise_optimized(float32_t *output, uint32_t length)
{
    // Use optimized random number generation
    static uint32_t seed = 12345;
    
    for (uint32_t i = 0; i < length; i++) {
        // Linear congruential generator (fast)
        seed = seed * 1664525 + 1013904223;
        
        // Convert to float in range [-1, 1]
        output[i] = ((float32_t)((int32_t)seed) / 2147483648.0f) * 1000.0f;
    }
}

static void melp_generate_pulse_train_optimized(float32_t pitch, float32_t *output, uint32_t length)
{
    float32_t period = AUDIO_SAMPLE_RATE_HZ / pitch;
    float32_t phase = 0.0f;
    
    // Generate optimized pulse train with better interpolation
    for (uint32_t i = 0; i < length; i++) {
        float32_t frac_phase = fmodf(phase, period) / period;
        
        // Improved pulse shape (Hann window)
        if (frac_phase < 0.1f) {
            float32_t window = 0.5f * (1.0f - cosf(2.0f * 3.14159265f * frac_phase / 0.1f));
            output[i] = 1000.0f * window;
        } else {
            output[i] = 0.0f;
        }
        
        phase += 1.0f;
    }
}

/* ========================================================================== */
/*                           WRAPPER FUNCTIONS                               */
/* ========================================================================== */

// Provide optimized versions as drop-in replacements
bool melp_decoder_init_fast(melp_decoder_state_t *state)
{
    return melp_decoder_init_optimized(state);
}

bool melp_decoder_decode_frame_fast(melp_decoder_state_t *state,
                                    const uint8_t *bit_stream,
                                    int16_t *output_samples)
{
    return melp_decoder_decode_frame_optimized(state, bit_stream, output_samples);
}

bool melp_decoder_deinit_fast(melp_decoder_state_t *state)
{
    return melp_decoder_deinit_optimized(state);
}
