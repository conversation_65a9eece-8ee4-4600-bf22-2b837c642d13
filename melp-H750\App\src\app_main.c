/* SPDX-License-Identifier: MIT */
/**
 * @file app_main.c
 * @brief Main application implementation
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

#include "app_main.h"
#include "project_config.h"
#include "hal_adc.h"
#include "hal_dac.h"
#include "hal_uart.h"
#include "hal_gpio.h"
#include "app_melp.h"

/* ========================================================================== */
/*                           PRIVATE VARIABLES                               */
/* ========================================================================== */

static app_state_t current_state = APP_STATE_IDLE;
static uint32_t systick_counter = 0;
static uint32_t led_blink_counter = 0;
static bool app_initialized = false;
static app_melp_context_t melp_context;

/* ========================================================================== */
/*                           PRIVATE FUNCTION DECLARATIONS                   */
/* ========================================================================== */

static void app_button_callback(bool pressed);
static void app_adc_callback(uint16_t sample);
static void app_uart_tx_callback(void);
// static void app_uart_rx_callback(uint8_t *data, uint16_t length);  // Commented out to remove warning
static void app_update_led(void);
static void app_melp_frame_encoded_callback(const uint8_t *frame_data, uint8_t frame_size);
static void app_melp_frame_decoded_callback(const int16_t *audio_data, uint32_t sample_count);
static void app_melp_state_changed_callback(app_melp_state_t old_state, app_melp_state_t new_state);
static void app_melp_error_callback(uint32_t error_code);

/* ========================================================================== */
/*                           PUBLIC FUNCTION IMPLEMENTATIONS                 */
/* ========================================================================== */

bool app_init(void)
{
    if (app_initialized) {
        return true;
    }

    // Initialize HAL drivers
    if (!hal_gpio_init()) {
        return false;
    }

    if (!hal_adc_init()) {
        return false;
    }

    if (!hal_dac_init()) {
        return false;
    }

    if (!hal_uart_init()) {
        return false;
    }

    // Set up callbacks
    hal_gpio_set_button_callback(app_button_callback);

    // Initialize MELP application
    app_melp_callbacks_t melp_callbacks = {
        .on_frame_encoded = app_melp_frame_encoded_callback,
        .on_frame_decoded = app_melp_frame_decoded_callback,
        .on_state_changed = app_melp_state_changed_callback,
        .on_error = app_melp_error_callback
    };

    if (!app_melp_init(&melp_context, &melp_callbacks)) {
        return false;
    }

    // Start with LED off
    hal_gpio_set_led(HAL_GPIO_STATE_LOW);

    current_state = APP_STATE_IDLE;
    app_initialized = true;

    return true;
}

void app_run(void)
{
    if (!app_initialized) {
        return;
    }

    // Main application loop
    while (1) {
        // Poll button in case interrupt is not used
        hal_gpio_button_poll();

        // Update LED based on current state
        app_update_led();

        // State machine processing
        switch (current_state) {
            case APP_STATE_IDLE:
                // Waiting for button press
                break;

            case APP_STATE_RECORDING:
                // Recording is handled by ADC callback
                break;

            case APP_STATE_PLAYING:
                // Playing is handled by DAC/UART
                break;

            case APP_STATE_ERROR:
                // Error state - blink LED rapidly
                break;

            default:
                current_state = APP_STATE_ERROR;
                break;
        }

        // Small delay to prevent busy waiting
        HAL_Delay(1);
    }
}

app_state_t app_get_state(void)
{
    return current_state;
}

void app_systick_handler(void)
{
    systick_counter++;
    led_blink_counter++;
}

/* ========================================================================== */
/*                           PRIVATE FUNCTION IMPLEMENTATIONS                */
/* ========================================================================== */

static void app_button_callback(bool pressed)
{
    if (pressed) {
        // Button pressed - start recording
        if (current_state == APP_STATE_IDLE) {
            current_state = APP_STATE_RECORDING;
            app_melp_start_recording(&melp_context);
            hal_adc_start_continuous(app_adc_callback);
            hal_gpio_set_led(HAL_GPIO_STATE_HIGH);
        }
    } else {
        // Button released - stop recording
        if (current_state == APP_STATE_RECORDING) {
            hal_adc_stop_continuous();
            app_melp_stop_recording(&melp_context);
            current_state = APP_STATE_IDLE;
            hal_gpio_set_led(HAL_GPIO_STATE_LOW);
        }
    }
}

static void app_adc_callback(uint16_t sample)
{
    // Convert ADC sample to signed 16-bit audio sample
    int16_t audio_sample = (int16_t)(sample - 2048); // Convert from 0-4095 to -2048 to +2047

    // Process audio sample through MELP encoder
    app_melp_process_input(&melp_context, &audio_sample, 1);
}

static void app_uart_tx_callback(void)
{
    // UART transmission complete
    // Nothing to do for now
}

/*  // Commented out to remove warning
static void app_uart_rx_callback(uint8_t *data, uint16_t length)
{
    // Process received MELP frames
    if (length >= MELP_FRAME_SIZE_BYTES) {
        for (uint16_t i = 0; i <= length - MELP_FRAME_SIZE_BYTES; i += MELP_FRAME_SIZE_BYTES) {
            app_melp_process_received_frame(&melp_context, &data[i], MELP_FRAME_SIZE_BYTES);
        }
    }
}
*/

static void app_update_led(void)
{
    switch (current_state) {
        case APP_STATE_IDLE:
            // LED off
            hal_gpio_set_led(HAL_GPIO_STATE_LOW);
            break;

        case APP_STATE_RECORDING:
            // LED on solid
            hal_gpio_set_led(HAL_GPIO_STATE_HIGH);
            break;

        case APP_STATE_PLAYING:
            // LED blinking slowly
            if (led_blink_counter >= (LED_BLINK_PERIOD_MS / 2)) {
                hal_gpio_toggle_led();
                led_blink_counter = 0;
            }
            break;

        case APP_STATE_ERROR:
            // LED blinking rapidly
            if (led_blink_counter >= (LED_BLINK_PERIOD_MS / 8)) {
                hal_gpio_toggle_led();
                led_blink_counter = 0;
            }
            break;

        default:
            break;
    }
}

static void app_melp_frame_encoded_callback(const uint8_t *frame_data, uint8_t frame_size)
{
    // Send encoded MELP frame via UART
    hal_uart_send_async(frame_data, frame_size, app_uart_tx_callback);
}

static void app_melp_frame_decoded_callback(const int16_t *audio_data, uint32_t sample_count)
{
    // Convert audio samples to DAC values and output
    for (uint32_t i = 0; i < sample_count; i++) {
        // Convert from signed 16-bit to 12-bit DAC value (0-4095)
        uint16_t dac_value = (uint16_t)(audio_data[i] + 2048);
        if (dac_value > 4095) dac_value = 4095;

        // Output to DAC (simplified - should use DMA for real-time)
        hal_dac_write_single(dac_value);
    }
}

static void app_melp_state_changed_callback(app_melp_state_t old_state, app_melp_state_t new_state)
{
    // Handle MELP state changes
    (void)old_state;
    (void)new_state;

    // Could update LED patterns based on MELP state
}

static void app_melp_error_callback(uint32_t error_code)
{
    // Handle MELP errors
    (void)error_code;

    // Set error state
    current_state = APP_STATE_ERROR;
}
