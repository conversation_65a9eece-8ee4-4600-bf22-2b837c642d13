/* SPDX-License-Identifier: MIT */
/**
 * @file project_config.h
 * @brief Project configuration constants and parameters
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

#ifndef PROJECT_CONFIG_H
#define PROJECT_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================== */
/*                           SYSTEM CONFIGURATION                            */
/* ========================================================================== */

/** System clock frequency in Hz */
#define SYSTEM_CLOCK_FREQ_HZ        (240000000UL)

/** Main crystal frequency in Hz */
#define HSE_FREQ_HZ                 (25000000UL)

/* ========================================================================== */
/*                           AUDIO CONFIGURATION                             */
/* ========================================================================== */

/** Audio sampling frequency in Hz */
#define AUDIO_SAMPLE_RATE_HZ        (8000U)

/** Audio frame length in milliseconds */
#define AUDIO_FRAME_LENGTH_MS       (22.5f)

/** Audio frame length in samples */
#define AUDIO_FRAME_LENGTH_SAMPLES  (180U)

/** ADC resolution in bits */
#define ADC_RESOLUTION_BITS         (12U)

/** DAC resolution in bits */
#define DAC_RESOLUTION_BITS         (12U)

/** ADC reference voltage in mV */
#define ADC_VREF_MV                 (3300U)

/* ========================================================================== */
/*                           MELP ALGORITHM CONFIGURATION                    */
/* ========================================================================== */

/** MELP bit rate in bits per second */
#define MELP_BIT_RATE_BPS           (600U)

/** MELP frame size in bits */
#define MELP_FRAME_SIZE_BITS        (14U)

/** MELP frame size in bytes */
#define MELP_FRAME_SIZE_BYTES       (2U)

/** MELP LPC analysis order */
#define MELP_LPC_ORDER              (10U)

/** MELP number of mixed excitation subbands */
#define MELP_NUM_SUBBANDS           (5U)

/* ========================================================================== */
/*                           UART CONFIGURATION                              */
/* ========================================================================== */

/** UART baud rate */
#define UART_BAUD_RATE              (921600UL)

/** UART data bits */
#define UART_DATA_BITS              (8U)

/** UART stop bits */
#define UART_STOP_BITS              (1U)

/** UART parity */
#define UART_PARITY                 (0U)  // No parity

/** UART TX buffer size */
#define UART_TX_BUFFER_SIZE         (512U)

/** UART RX buffer size */
#define UART_RX_BUFFER_SIZE         (512U)

/* ========================================================================== */
/*                           GPIO CONFIGURATION                              */
/* ========================================================================== */

/** LED GPIO port */
#define LED_GPIO_PORT               GPIOC

/** LED GPIO pin */
#define LED_GPIO_PIN                GPIO_PIN_13

/** Button GPIO port */
#define BUTTON_GPIO_PORT            GPIOC

/** Button GPIO pin */
#define BUTTON_GPIO_PIN             GPIO_PIN_1

/** ADC input GPIO port */
#define ADC_GPIO_PORT               GPIOA

/** ADC input GPIO pin */
#define ADC_GPIO_PIN                GPIO_PIN_3

/** DAC output GPIO port */
#define DAC_GPIO_PORT               GPIOA

/** DAC output GPIO pin */
#define DAC_GPIO_PIN                GPIO_PIN_5

/* ========================================================================== */
/*                           TIMING CONFIGURATION                            */
/* ========================================================================== */

/** System tick frequency in Hz */
#define SYSTEM_TICK_FREQ_HZ         (1000U)

/** Audio timer frequency in Hz */
#define AUDIO_TIMER_FREQ_HZ         AUDIO_SAMPLE_RATE_HZ

/** LED blink period in milliseconds */
#define LED_BLINK_PERIOD_MS         (500U)

/* ========================================================================== */
/*                           BUFFER CONFIGURATION                            */
/* ========================================================================== */

/** Audio input buffer size in samples */
#define AUDIO_INPUT_BUFFER_SIZE     (AUDIO_FRAME_LENGTH_SAMPLES * 2)

/** Audio output buffer size in samples */
#define AUDIO_OUTPUT_BUFFER_SIZE    (AUDIO_FRAME_LENGTH_SAMPLES * 2)

/** MELP encoded data buffer size in bytes */
#define MELP_ENCODED_BUFFER_SIZE    (MELP_FRAME_SIZE_BYTES * 16)

/* ========================================================================== */
/*                           PROTOCOL CONFIGURATION                          */
/* ========================================================================== */

/** Protocol start byte 1 */
#define PROTOCOL_START_BYTE1        (0x55U)

/** Protocol start byte 2 */
#define PROTOCOL_START_BYTE2        (0xAAU)

/** Protocol pause byte */
#define PROTOCOL_PAUSE_BYTE         (0xFEU)

/** Protocol maximum frame sequence number */
#define PROTOCOL_MAX_FRAME_SEQ      (0xFFU)

/* ========================================================================== */
/*                           PERFORMANCE CONFIGURATION                       */
/* ========================================================================== */

/** Maximum frame processing time in microseconds */
#define MAX_FRAME_PROCESSING_TIME_US (22500U)

/** DMA timeout in milliseconds */
#define DMA_TIMEOUT_MS              (100U)

/** UART timeout in milliseconds */
#define UART_TIMEOUT_MS             (50U)

#ifdef __cplusplus
}
#endif

#endif /* PROJECT_CONFIG_H */
