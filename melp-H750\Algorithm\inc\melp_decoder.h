/* SPDX-License-Identifier: MIT */
/**
 * @file melp_decoder.h
 * @brief MELP (Mixed Excitation Linear Prediction) Decoder Interface
 * <AUTHOR> Assistant
 * @date 2025-01-19
 * 
 * Based on MIL-STD-3005 and STANAG-4591 specifications
 * Implements 600bps MELP vocoder decoder
 */

#ifndef MELP_DECODER_H
#define MELP_DECODER_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "melp_encoder.h"  // For shared types
#include "project_config.h"

/* ========================================================================== */
/*                           MELP DECODER CONSTANTS                          */
/* ========================================================================== */

/** Synthesis filter memory length */
#define MELP_SYNTH_MEM_SIZE         MELP_LPC_ORDER

/** Pitch synthesis buffer size */
#define MELP_PITCH_BUF_SIZE         (MELP_PITCH_MAX + MELP_FRAME_SIZE)

/** Post-filter order */
#define MELP_POSTFILT_ORDER         10

/** Adaptive spectral enhancement factor */
#define MELP_ASE_FACTOR             0.8f

/** Pulse dispersion factor */
#define MELP_PULSE_DISP_FACTOR      0.25f

/* ========================================================================== */
/*                           TYPE DEFINITIONS                                */
/* ========================================================================== */

/**
 * @brief MELP decoder state structure
 */
typedef struct {
    float synth_mem[MELP_SYNTH_MEM_SIZE];      /**< Synthesis filter memory */
    float pitch_buf[MELP_PITCH_BUF_SIZE];     /**< Pitch synthesis buffer */
    float postfilt_mem[MELP_POSTFILT_ORDER];  /**< Post-filter memory */
    float prev_lsf[MELP_LPC_ORDER];           /**< Previous LSF coefficients */
    float prev_gain;                           /**< Previous gain value */
    float prev_pitch;                          /**< Previous pitch value */
    uint32_t pitch_ptr;                        /**< Pitch buffer pointer */
    uint32_t frame_count;                      /**< Frame counter */
    bool prev_voiced;                          /**< Previous voicing state */
    bool initialized;                          /**< Initialization flag */
} melp_decoder_state_t;

/**
 * @brief Mixed excitation filter bank structure
 */
typedef struct {
    float coeffs[MELP_NUM_BANDS][8];          /**< Filter coefficients per band */
    float mem[MELP_NUM_BANDS][8];             /**< Filter memory per band */
} melp_mixed_excitation_t;

/* ========================================================================== */
/*                           FUNCTION DECLARATIONS                           */
/* ========================================================================== */

/**
 * @brief Initialize MELP decoder
 * @param state Pointer to decoder state structure
 * @return true if successful, false otherwise
 */
bool melp_decoder_init(melp_decoder_state_t *state);

/**
 * @brief Deinitialize MELP decoder
 * @param state Pointer to decoder state structure
 * @return true if successful, false otherwise
 */
bool melp_decoder_deinit(melp_decoder_state_t *state);

/**
 * @brief Decode one frame from bit stream
 * @param state Pointer to decoder state structure
 * @param bit_stream Input bit stream (2 bytes for 14 bits)
 * @param output_samples Output speech samples (180 samples)
 * @return true if successful, false otherwise
 */
bool melp_decoder_decode_frame(melp_decoder_state_t *state,
                               const uint8_t *bit_stream,
                               int16_t *output_samples);

/**
 * @brief Unpack bit stream into encoded frame
 * @param bit_stream Input bit stream (2 bytes)
 * @param encoded_frame Output encoded frame structure
 * @return Number of bits unpacked
 */
uint8_t melp_decoder_unpack_frame(const uint8_t *bit_stream,
                                  melp_encoded_frame_t *encoded_frame);

/**
 * @brief Handle frame erasure (packet loss)
 * @param state Pointer to decoder state structure
 * @param output_samples Output speech samples (180 samples)
 * @return true if successful, false otherwise
 */
bool melp_decoder_frame_erasure(melp_decoder_state_t *state,
                                int16_t *output_samples);

/**
 * @brief Get decoder version information
 * @return Version string
 */
const char* melp_decoder_get_version(void);

/* ========================================================================== */
/*                           INTERNAL FUNCTION DECLARATIONS                  */
/* ========================================================================== */

/**
 * @brief Dequantize MELP parameters
 * @param encoded_frame Input quantized parameters
 * @param params Output MELP parameters
 * @return true if successful, false otherwise
 */
bool melp_dequantize_parameters(const melp_encoded_frame_t *encoded_frame,
                                melp_params_t *params);

/**
 * @brief Convert Line Spectral Frequencies to LPC coefficients
 * @param lsf_coeffs Input LSF coefficients
 * @param lpc_coeffs Output LPC coefficients
 * @return true if successful, false otherwise
 */
bool melp_lsf_to_lpc(const float *lsf_coeffs, float *lpc_coeffs);

/**
 * @brief Generate mixed excitation signal
 * @param pitch Pitch value
 * @param voicing Voicing strengths per band
 * @param aperiodic_flag Aperiodic pulse flag
 * @param excitation Output excitation signal
 * @param length Signal length
 * @return true if successful, false otherwise
 */
bool melp_generate_excitation(float pitch, const float *voicing,
                              bool aperiodic_flag, float *excitation,
                              uint32_t length);

/**
 * @brief Apply LPC synthesis filter
 * @param excitation Input excitation signal
 * @param lpc_coeffs LPC coefficients
 * @param synth_mem Synthesis filter memory
 * @param output Output synthesized speech
 * @param length Signal length
 * @return true if successful, false otherwise
 */
bool melp_lpc_synthesis(const float *excitation, const float *lpc_coeffs,
                        float *synth_mem, float *output, uint32_t length);

/**
 * @brief Apply adaptive spectral enhancement
 * @param input Input synthesized speech
 * @param lpc_coeffs LPC coefficients
 * @param output Output enhanced speech
 * @param length Signal length
 * @return true if successful, false otherwise
 */
bool melp_adaptive_spectral_enhancement(const float *input,
                                        const float *lpc_coeffs,
                                        float *output, uint32_t length);

/**
 * @brief Apply post-filter
 * @param input Input speech signal
 * @param lpc_coeffs LPC coefficients
 * @param postfilt_mem Post-filter memory
 * @param output Output filtered speech
 * @param length Signal length
 * @return true if successful, false otherwise
 */
bool melp_post_filter(const float *input, const float *lpc_coeffs,
                      float *postfilt_mem, float *output, uint32_t length);

/**
 * @brief Apply pulse dispersion filter
 * @param input Input speech signal
 * @param output Output dispersed speech
 * @param length Signal length
 * @return true if successful, false otherwise
 */
bool melp_pulse_dispersion(const float *input, float *output, uint32_t length);

#ifdef __cplusplus
}
#endif

#endif /* MELP_DECODER_H */
