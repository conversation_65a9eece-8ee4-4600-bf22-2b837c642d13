# 低码率语音压缩技术调研报告

## 背景

随着通信技术的不断发展，特别是在带宽受限的场景（如无线通信、业余无线电和VoIP）中，低码率语音压缩技术变得尤为重要。本报告旨在调研当前主流的语音压缩技术，重点关注码率在2.4 kbps或更低的解决方案，并对比其硬件要求和语音质量表现。

## 调研目标

1. 识别当前存在的低码率语音压缩技术。
2. 评估这些技术在码率、硬件要求和语音质量方面的表现。
3. 提供一份综合对比分析，为后续技术选型提供参考。

## 技术概述

以下是调研中发现的几种主要低码率语音压缩技术：

### 1. Compacketer (Compandent)
- **码率**：2.8 kbps
- **特点**：采用创新技术，在质量、码率和复杂性之间取得突破，适用于VoIP和无线应用，具有较好的帧丢失抗性。
- **硬件要求**：未具体说明，但强调低复杂性，适合多种设备。
- **语音质量**：主观测试显示其质量在2.8 kbps下优于ITU-T G.723.1的5.3 kbps，略优于6.3 kbps。

### 2. LPCNet
- **码率**：1.6 kbps
- **特点**：结合信号处理和深度学习，实时运行在普通CPU甚至手机上，压缩比高达160倍。
- **硬件要求**：可在单核CPU上实时运行，例如在Snapdragon 855上只需31%的单核性能。
- **语音质量**：质量远超MELP在2.4 kbps的表现，接近Opus在9 kbps的质量。

### 3. Codec 2
- **码率**：支持多种模式，从450 bit/s到3200 bit/s
- **特点**：开源无专利费，专为业余无线电设计，使用正弦编码技术。
- **硬件要求**：可在多种平台上运行，包括嵌入式设备，但对低端设备可能有性能挑战。
- **语音质量**：700 bit/s和450 bit/s模式下仍有可接受的质量，适用于极低带宽场景。

### 4. 基于MELP的600bps Vocoder
- **码率**：600 bit/s
- **特点**：基于混合激励线性预测（MELP），进一步降低码率。
- **硬件要求**：未具体说明，但MELP类算法通常对计算资源要求较低。
- **语音质量**：未提供具体质量数据，但MELP在低码率下通常质量有限。

### 5. 基于混合激励的3.6kbps算法
- **码率**：3.6 kbps
- **特点**：低复杂度设计，使用标量量化线谱频率（LSF）和动态U/V阈值改进语音质量。
- **硬件要求**：强调低复杂度，适合资源受限设备。
- **语音质量**：PESQ-MOS测试显示达到通信质量，尤其对高频女声有改进。

### 6. AMBE (Advanced Multi-Band Excitation)
- **码率**：支持多种模式，低至2.4 kbps（如AMBE+2可达2.0 kbps）
- **特点**：基于多带激励（MBE）模型，提供高质量语音，广泛用于数字移动无线电、卫星通信和安全通信。
- **硬件要求**：低复杂度，可在低成本DSP芯片上实现，适合嵌入式设备。
- **语音质量**：在3.6 kbps下优于8 kbps的VSELP，尤其在背景噪声下表现突出；AMBE+2在4 kbps下达到收费质量。

### 7. ASELP (Advanced Sinusoidal Excitation Linear Prediction)
- **码率**：支持多种模式，低至1.2 kbps和2.4 kbps
- **特点**：基于正弦激励线性预测，由清华大学开发，专为数字集群系统设计，具有高鲁棒性。
- **硬件要求**：低资源需求，在TMS320C55x DSP上只需2.5 MIPS计算量，存储需求低。
- **语音质量**：MOS测试显示2.4 kbps下优于AMBE+2（3.364 vs 3.237）和MELPe（3.262 vs 2.987）。

### 8. IMBE (Improved Multi-Band Excitation)
- **码率**：支持多种模式，低至2.4 kbps（如7.2 kbps模式包含4.4 kbps语音编码）
- **特点**：基于多带激励（MBE）模型，是AMBE的前身，广泛用于公共安全无线电和卫星通信，具有较强的背景噪声和信道错误鲁棒性。
- **硬件要求**：低复杂度，可在低成本DSP芯片上实现，适合嵌入式设备。
- **语音质量**：在APCO Project 25测试中，7.2 kbps下显著优于VSELP、STC和CELP，尤其在噪声环境下表现突出。

## 对比分析

以下是对调研中提到的低码率语音压缩技术的详细对比，特别关注硬件复杂度和语音质量。硬件复杂度将以低、中、高三个等级进行评估，基于计算资源需求和对嵌入式设备的适应性；语音质量将基于主观测试（如MOS分数）或相对比较进行描述。特别标注了码率在1kbps左右或以下的技术，以满足极低码率需求。

| 技术名称                     | 码率          | 硬件复杂度       | 硬件要求详情                              | 语音质量等级        | 语音质量详情                              | 接近1kbps备注          |
|-----------------------------|------------------|-----------------|------------------------------------------|-------------------|------------------------------------------|-----------------------|
| Compacketer (Compandent)    | 2.8 kbps     | 低              | 低复杂性，具体未明                       | 高                | 优于G.723.1 5.3kbps，略优于6.3kbps       |                       |
| LPCNet                      | 1.6 kbps     | 中              | 单核CPU即可实时运行（如Snapdragon 855）   | 极高              | 远超MELP 2.4kbps，接近Opus 9kbps        | 接近1kbps             |
| Codec 2                     | 450-3200 bps | 中              | 多种平台，嵌入式设备可能有挑战            | 中等              | 700bps和450bps下质量可接受               | 450bps低于1kbps       |
| 基于MELP的600bps Vocoder    | 600 bps      | 低              | 通常较低，具体未明                       | 低                | 质量数据不足，MELP通常质量有限           | 600bps低于1kbps       |
| 基于混合激励的3.6kbps算法   | 3.6 kbps     | 低              | 低复杂度，适合资源受限设备                | 中高              | PESQ-MOS测试达到通信质量                 |                       |
| AMBE                        | 2.0-7.2 kbps | 低              | 低复杂度，适合嵌入式设备                  | 高                | 3.6kbps优于8kbps VSELP，4kbps收费质量   |                       |
| ASELP                       | 1.2-2.4 kbps | 低              | 低资源，2.5 MIPS，适合嵌入式设备          | 高                | 2.4kbps MOS优于AMBE+2和MELPe            | 1.2kbps接近1kbps      |
| IMBE                        | 2.4-7.2 kbps | 低              | 低复杂度，适合嵌入式设备                  | 高                | 7.2kbps优于VSELP、STC和CELP             |                       |

## 初步结论

- **码率最低**：Codec 2的450 bit/s模式和基于MELP的600 bit/s vocoder提供了最低码率选项，均低于1kbps，适合极低带宽场景；ASELP的1.2 kbps和LPCNet的1.6 kbps也接近1kbps，值得关注。
- **质量最佳**：LPCNet在1.6 kbps下表现出色，质量接近较高码率的传统编解码器（如Opus 9 kbps），是低码率高质量的代表；AMBE、ASELP和IMBE也在低码率下提供了良好的语音质量。
- **硬件友好**：LPCNet、AMBE、ASELP、IMBE和基于混合激励的3.6kbps算法明确提到低复杂性，适合资源受限的嵌入式设备。
- **硬件复杂度专项分析**：AMBE、ASELP、IMBE、Compacketer、基于混合激励的3.6kbps算法和基于MELP的600bps Vocoder在硬件复杂度上表现为"低"，对嵌入式设备友好，适合资源受限场景；LPCNet和Codec 2的硬件复杂度为"中"，可能需要更强的处理器支持，尤其在低端设备上可能有挑战。
- **语音质量专项分析**：LPCNet的语音质量为"极高"，在极低码率下表现最佳；Compacketer、AMBE、ASELP和IMBE的语音质量为"高"，在2.0-7.2 kbps范围内提供了接近或超过传统较高码率技术的质量；基于混合激励的3.6kbps算法为"中高"，达到通信质量；Codec 2为"中等"，在极低码率下质量尚可接受；基于MELP的600bps Vocoder为"低"，质量有限。

## 后续工作

1. 进一步收集其他低码率语音压缩技术的信息，确保调研全面性。
2. 深入分析各技术的应用场景和限制条件。
3. 根据具体应用需求（如带宽限制、硬件平台），推荐最合适的技术方案。

---

**报告编写者**：AI助手
**日期**：2025年 